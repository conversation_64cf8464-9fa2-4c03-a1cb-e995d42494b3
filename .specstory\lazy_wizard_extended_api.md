# 懒人精灵API完整参考文档

## 数学方法

### 基础数学运算

**绝对值**
```lua
语法: math.abs(number)
参数: number - 可以是任意有效的数值表达式
返回值: 浮点型,返回数字的绝对值
```

**向上取整**
```lua
语法: math.ceil(number)
参数: number - 可以是任意有效的数值表达式
返回值: 整数型,返回不小于number的最大整数
```

**向下取整**
```lua
语法: math.floor(number)
参数: number - 可以是任意数值表达式
返回值: 整数型,返回不大于number的最大整数
```

**取余数**
```lua
语法: math.fmod(被除数,除数)
参数: 被除数,除数 - 可以是任意有效的表达式
返回值: 浮点型,返回余数
```

**取整数和小数部分**
```lua
语法: math.modf(number)
参数: number - 可以是任意有效的数值表达式
返回值: 数组型,返回小数的整数部分和小数部分
```

**转化整数**
```lua
语法: math.tointeger(n)
功能说明: 如果x可以转换为一个整数，返回该整数。否则返回nil
参数: 字符串类型或者数字类型
返回值: 转化的整数
```

**获取数字类型**
```lua
语法: math.type(n)
功能说明: 如果x是整数，返回"integer"，如果它是浮点数，返回"float"，如果x不是数字，返回nil
参数: 数字类型
返回值: 字符串类型
```

**无符号数据比较**
```lua
语法: math.ult(m,n)
功能说明: 如果整数m和n以无符号整数形式比较，m在n之下，返回布尔真否则返回假
参数: 数字类型
返回值: 布尔类型
```

### 三角函数

**正弦**
```lua
语法: math.sin(number)
参数: number - 可以是0-2pi之间的数值表达式
返回值: 浮点型,返回正弦值
```

**余弦**
```lua
语法: math.cos(number)
参数: number - 可以是0-2pi之间的数值表达式
返回值: 浮点型,返回余弦值
```

**正切**
```lua
语法: math.tan(rad)
参数: rad - 可以是0-2pi之间的数值表达式
返回值: 浮点型,返回rad的正切值
```

**反正弦**
```lua
语法: math.asin(number)
参数: number - 可以是-1,1之间的数值表达式
返回值: 浮点型,返回反正弦值
```

**反余弦**
```lua
语法: math.acos(number)
参数: number - 可以是-1,1之间的数值表达式
返回值: 浮点型,返回反余弦值
```

**反正切**
```lua
语法: math.atan(number)
参数: number - 可以是任意有效的数值表达式
返回值: 浮点型,返回反正切值
```

### 角度和弧度转换

**弧度转角度**
```lua
语法: math.deg(number)
参数: number - 可以是以弧度表达式
返回值: 浮点型,返回弧度转角度值
```

**角度转弧度**
```lua
语法: math.rad(rad)
参数: rad - 是任意一个角度值
返回值: 数组型,返回rad的弧度值
```

### 指数和对数

**e为底x次方值**
```lua
语法: math.exp(number)
参数: number - 可以是任意数值表达式
返回值: 浮点型,返回e为底number次方值
```

**数字的自然对数**
```lua
语法: math.log(number)
参数: number - 可以是任意有效的数值表达式
返回值: 浮点型,返回number的自然对数
```

**开根号**
```lua
语法: math.sqrt(number)
参数: number - 可以是任意的数值表达式
返回值: 浮点型,返回number的开根号值
```

### 最值函数

**取得参数中最大值**
```lua
语法: math.max(number1,number2,number,....)
参数: number1,number2,... - 可以是任意有效的数值表达式
返回值: 浮点型,返回参数列表中最大的值
```

**取得参数中最小值**
```lua
语法: math.min(number1,number2,number,....)
参数: number1,number2,... - 可以是任意有效的数值表达式
返回值: 浮点型,返回参数列表中最小的值
```

### 随机数和常量

**圆周率**
```lua
圆周率: 3.1415926535898
print(math.pi)
```

**设随机数种子**
```lua
语法: math.randomseed(seed)
参数: seed - 可以是任意有效的数值表达式
```

**产生随机数**
```lua
语法: math.random(start,end)
参数: start起始值,end结束值
返回值: 数组型,返回一个从start到end区间中的一个值
```

## 加解密方法

### AES加密

**AES 加密/解密**
```lua
语法: cryptLib.aes_crypt(data, key, operation, mode, [iv], [padding])
参数说明:
  data: 要加密/解密的原始数据(string)
  key: 加密密钥(16/24/32字节对应AES-128/192/256)
  operation: 操作类型("encrypt"或"decrypt")
  mode: 加密模式("ecb","cbc","cfb","ofb","ctr")
  iv: 初始化向量(16字节字符串，ecb模式不需要)
  padding: 是否启用PKCS#7填充(可选，默认为true)
返回值: 加密/解密后的数据(string)
注意事项:
  1. ECB模式不需要iv参数
  2. 加解密需要使用相同的参数配置
  3. 密钥长度必须为16/24/32字节
  4. iv长度必须为16字节(当模式不是ecb时)
```

**生成随机初始化向量(IV)**
```lua
语法: cryptLib.aes_ivgen()
参数说明: 无
返回值: 16字节的随机IV(string)
```

**生成随机AES密钥**
```lua
语法: cryptLib.aes_keygen(key_length)
参数说明:
  key_length: 密钥长度(16/24/32对应AES-128/192/256)
返回值: 随机生成的密钥(string)
```

### RSA加密

**RSA解密**
```lua
语法: decrypted = cryptLib.rsa_decrypt(data, key, is_public_key)
参数说明:
  data: 要解密的数据(字符串)
  key: 公钥或私钥(PEM格式)
  is_public_key: true表示使用公钥解密，false表示使用私钥解密
返回值: 解密后的原始数据
注意事项:
  1. 通常使用私钥解密公钥加密的数据
  2. 也可以使用公钥解密私钥加密的数据(用于签名验证)
  3. 确保使用与加密时匹配的密钥对
```

**RSA加密**
```lua
语法: encrypted = cryptLib.rsa_encrypt(data, key, is_public_key)
参数说明:
  data: 要加密的数据(字符串)
  key: 公钥或私钥(PEM格式)
  is_public_key: true表示使用公钥加密，false表示使用私钥加密
返回值: 加密后的数据
注意事项:
  1. RSA加密有长度限制，加密数据长度不能超过密钥长度-11字节(对于PKCS1填充)
  2. 通常使用公钥加密，私钥解密
  3. 加密大文件应使用混合加密(用RSA加密对称密钥)
```

**生成RSA密钥对**
```lua
语法: pubkey, privkey = cryptLib.rsa_generate_key([key_bits])
参数说明:
  key_bits: 密钥长度(可选，默认2048，常见值1024/2048/4096)
返回值: 公钥(PEM格式), 私钥(PEM格式)
注意事项:
  1. 密钥长度越长越安全，但性能越低
  2. 1024位密钥已不推荐用于新系统
  3. 返回的PEM格式密钥可以直接保存到文件
```

## lfs库方法

### 文件和目录信息

**获取文件或目录属性**
```lua
语法: lfs.attributes(path)
参数说明: path - 指定要获取的文件或者文件夹的绝对路径
返回值: 表类型，包含文件或文件的所有信息
```

**切换目录**
```lua
语法: lfs.chdir(path)
参数说明: path - 指定要切换到的新目录的绝对路径
返回值: 无
```

**获取当前目录**
```lua
语法: lfs.currentdir()
参数说明: 无
返回值: 字符串，包含当前工作目录的绝对路径
```

**遍历目录**
```lua
语法: lfs.dir(path)
参数说明: path - 指定要遍历的目录的绝对路径
返回值: 表类型，包含目录中的文件和子目录信息
```

**获取符号链接文件属性**
```lua
语法: lfs.symlinkattributes(path)
参数说明: path - 指定要获取属性的符号链接文件的路径
返回值: 表类型，包含符号链接文件的属性信息
```

### 文件和目录操作

**创建文件夹**
```lua
语法: lfs.mkdir(path)
参数说明: path - 指定要创建的文件夹的路径
返回值: bool类型，表示是否成功创建文件夹
```

**删除空文件夹**
```lua
语法: lfs.rmdir(path)
参数说明: path - 指定要删除的空文件夹的路径
返回值: bool类型，表示是否成功删除文件夹
```

**创建硬链接**
```lua
语法: lfs.link(src, dest)
参数说明: 
  src - 指定原始文件的路径
  dest - 指定新链接的路径
返回值: 成功返回0，否则返回空
```

**更新文件时间**
```lua
语法: lfs.touch(path)
参数说明: path - 指定要更新时间的文件的路径
返回值: bool类型，表示是否成功更新文件时间
```

### 文件锁定

**锁定文件**
```lua
语法: lfs.lock(file, mode)
参数说明: 
  file - 要锁定的文件表
  mode - 锁定模式，支持"r"表示读取锁定，"w"表示写入锁定，"u"表示解锁
返回值: bool类型，表示是否成功锁定文件
```

**锁定目录**
```lua
语法: lfs.lock_dir(path)
参数说明: path - 指定要锁定的目录路径
返回值: bool类型，表示是否成功锁定目录
```

**解锁文件**
```lua
语法: lfs.unlock(file)
参数说明: file - 要解锁的文件表
返回值: bool类型，表示是否成功解锁文件
```

## ffi库方法

**定义C语言的类型和函数**
```lua
语法: ffi.cdef(str)
参数说明: str - 该字符串包含C语言的类型声明和函数原型
返回值: 返回为空
```

**加载动态库**
```lua
语法: ffi.load(str)
参数说明: str - 该字符串指定要加载的库的路径和名称
返回值: 返回一个指针对象
```

## luasocket部分方法

**发起网络请求**
```lua
语法: http.request(arg)
参数说明: arg可以是如下数据：
  url: 一个字符串，表示要请求的URL
  method: 一个字符串，表示HTTP请求方法，通常是"GET"或"POST"
  headers: 一个表（table），包含HTTP请求头部的键值对
  chunked: 一个布尔值，表示是否使用分块传输编码
  version: 一个数字，表示HTTP协议版本。默认为1.1
  keepAlive: 一个布尔值，表示是否保持连接。默认为false
  proxy: 一个表，包含代理服务器的信息。包括代理类型（"http"或"ftp"）、地址、端口和用户名等信息
  proxyCreds: 一个表，包含代理服务器的认证信息（如果需要）。包括用户名和密码
  cookie: 一个字符串，表示要发送的Cookie
  sslverify: 一个布尔值，表示是否验证SSL证书。默认为true（推荐）
  timeout: 一个数字，表示请求超时的秒数。默认为nil（不超时）
  norequestheaders: 一个布尔值，表示是否不发送请求头。默认为false
  requestheaders: 一个表，包含自定义的请求头信息
返回值: 如果请求成功，http.request返回一个表（table），其中包含服务器返回的HTTP头信息和主体内容。这个表通常具有以下几个字段：
  statuscode: 表示HTTP响应状态码（如200、404等）
  reason: 表示HTTP响应状态码对应的文本描述
  headers: 表示服务器返回的HTTP头部信息，是一个表（table）包含各种头部字段
  content: 表示服务器的响应主体内容，是一个字符串
  如果请求失败，http.request返回nil，并且可能会产生一个错误消息
```

## luaopencv部分扩展方法

### 内存管理

**回收cv.new*开头申请的内存**
```lua
语法: cv.deletePtr(ptr)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型
返回值: 空
```

### 指针创建方法

**new一个字节指针**
```lua
语法: cv.newByte(val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: val - 字节型初始化这指针的值
返回值: 返回一个字节型指针
```

**new一个整型指针**
```lua
语法: cv.newInt(val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: val - 整数型初始化这指针的值
返回值: 返回一个整形指针
```

**new一个长整型指针**
```lua
语法: cv.newLong(val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: val - 长整型初始化这指针的值
返回值: 返回一个长整型指针
```

**new一个单精度浮点指针**
```lua
语法: cv.newFloat(val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: val - 单精度浮点型初始化这指针的值
返回值: 返回一个单精度浮点指针
```

**new一个双精度浮点指针**
```lua
语法: cv.newDouble(val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: val - 双精度浮点型初始化这指针的值
返回值: 返回一个双精度浮点指针
```

**new一个cv::Point指针**
```lua
语法: cv.newPoint(x,y)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的。
      懒人已集成所有opencv4.5.3的函数，都能在lua层直接直接调用，下面只是一个展示例子，更多函数使用请自行参考opencv4.5.3参考文档
参数说明: x,y - 整数型坐标
返回值: 返回一个cv::Point指针
```

**new一个cv::Point2f指针**
```lua
语法: cv.newPoint2f(x,y)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的。
      懒人已集成所有opencv4.5.3的函数，都能在lua层直接直接调用，下面只是一个展示例子，更多函数使用请自行参考opencv4.5.3参考文档
参数说明: x,y - 浮点型型坐标
返回值: 返回一个cv::Point2f指针
```

### 指针值获取方法

**获取字节指针的值**
```lua
语法: cv.getByte(ptr)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型
返回值: 返回指针指向内存的字节值
```

**获取整型指针的值**
```lua
语法: cv.getInt(ptr)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型
返回值: 返回指针指向内存的整数值
```

**获取长整型指针的值**
```lua
语法: cv.getLong(ptr)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型
返回值: 返回指针指向内存的长整型值
```

**获取单精度浮点指针的值**
```lua
语法: cv.getFloat(ptr)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型
返回值: 返回指针指向内存的浮点值
```

**获取双精度浮点指针的值**
```lua
语法: cv.getDouble(ptr)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型
返回值: 返回指针指向内存的浮点值
```

**获取Point指针的值**
```lua
语法: cv.getPoint(ptr)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型
返回值: 返回指针指向cv::Point对象
```

### 指针值设置方法

**修改字节指针的值**
```lua
语法: cv.setByte(ptr,val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型, val - 字节型
返回值: 空
```

**修改整型指针的值**
```lua
语法: cv.setInt(ptr,val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型, val - 整型
返回值: 空
```

**修改长整型指针的值**
```lua
语法: cv.setLong(ptr,val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型, val - 长整型
返回值: 空
```

**修改单精度浮点指针的值**
```lua
语法: cv.setFloat(ptr,val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型, val - 单精度
返回值: 空
```

**修改双精度浮点指针的值**
```lua
语法: cv.setDouble(ptr,val)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型, val - 双精度
返回值: 空
```

**设置Point指针的值**
```lua
语法: cv.setPoint(ptr,x,y)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型, x,y - 坐标
返回值: 空
```

**设置Point2f指针的值**
```lua
语法: cv.setPoint2f(ptr,x,y)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的
参数说明: ptr - 指针类型, x,y - 坐标
返回值: 空
```

### 截图功能

**区域截图并返回Mat矩阵**
```lua
语法: cv.snapShot(l,t,r,b)
说明: 这个函数不是opencv官方函数，这个是基于懒人扩展出来方便对截图直接opencv4.5.3进行图色变化的。
      懒人已集成所有opencv4.5.3的函数，都能在lua层直接直接调用，下面只是一个展示例子，更多函数使用请自行参考opencv4.5.3参考文档
参数说明: l - 左范围, t - 上范围, r - 右范围, b - 下范围
返回值: 返回一个Mat矩阵
```

## 文件方法

**打开一个文件输入**
```lua
语法: io.input([file])
参数说明:
  file: 可以是字符串路径也可以是以io.open打开的句柄
  用文件名调用它时,(以文本模式)来打开该名字的文件,
  并将文件句柄设为默认输入文件.如果用文件句柄去调用它,
  就简单的将该句柄设为默认输入文件.如果调用时不传参数,它返回当前的默认输入文件
返回值: 文件句柄,在出错的情况下,函数抛出错误而不是返回错误码
功能说明: 提供一个循环迭代器以遍历文件,
  如果指定了文件名则当遍历结束后将自动关闭该文件;
  若使用默认文件,则遍历结束后不会自动关闭文件。
```

**打开一个文件**
```lua
语法: io.open(path,mode)
参数说明:
  path: 字符串类型表示要打开的文件路径
  mode: 文件打开的模式,如下
    "r": 读模式 (默认);
    "w": 写模式;
    "a": 添加模式;
    "r+": 更新模式,所有之前的数据将被保存
    "w+": 更新模式,所有之前的数据将被清除
    "a+": 添加更新模式,所有之前的数据将被保存,只允许在文件尾进行添加
    "b": 某些系统支持二进制方式
返回值: 文件句柄,失败则返回nil+错误信息
```

**打开一个文件输出**
```lua
语法: io.output([file])
参数说明:
  file: 可以是字符串路径也可以是以io.open打开的句柄
  用文件名调用它时,以文本模式来打开该名字的文件,
  并将文件句柄设为默认输出文件.如果用文件句柄去调用它,
  就简单的将该句柄设为默认输出文件.如果调用时不传参数,
  它返回当前的默认输出文件.在出错的情况下,函数抛出错误而不是返回错误码。
```

**读取文件数据**
```lua
语法: io.read() 
等价于: io.input():read(···)
```

**写入数据到文件**
```lua
语法: io.write() 
等价于: io.output():write(···)
```

## 时间方法

**获取网络时间**
```lua
语法: getNetWorkTime()
返回值: 格式为年-月-日_时-分-秒的时间字符串
```

**日期格式化**
```lua
语法: os.date(format [, time])
功能说明: 用来格式化时间戳为可读时间,time为可选时间戳,省略时取当下
```

**时间戳**
```lua
语法: os.time([table])
功能说明: 默认获取当前时间戳,也可以指定table参数
```

**系统时间戳单位是毫秒**
```lua
语法: systemTime()
```

**脚本运行时间单位是毫秒**
```lua
语法: tickCount()
```

## 数组方法

**获取数组长度**
```lua
语法: #array
功能说明: #array,获取数组长度
```

**返回数组元素连接的字符串**
```lua
语法: table.concat(table [, sep [, start [, end]]])
功能说明: concat是concatenate(连锁, 连接)的缩写.
  table.concat()函数列出参数中指定table的数组部分
  从start位置到end位置的所有元素, 元素间以指定的分隔符(sep)隔开。
```

**数组中插入新元素**
```lua
语法: table.insert(table, [pos,] value)
功能说明: 在table的数组部分指定位置(pos)插入值
  为value的一个元素. pos参数可选, 默认为数组部分末尾.
```

**数组移除元素**
```lua
语法: table.remove(table [, pos])
功能说明: 返回table数组部分位于pos位置的元素.
  其后的元素会被前移. pos参数可选, 默认为table长度, 即从最后一个元素删起。
```

**数组排序**
```lua
语法: table.sort(table [, comp])
功能说明: 对给定的table进行升序排序。
```

## 字符串方法

### 基础字符串操作

**字符串分割**
```lua
语法: splitStr(src,matchstr)
参数说明: src - 源字符串, matchstr - 分隔符
```

**转换字符为整数值**
```lua
语法: string.byte(s [, i [, j]])
功能说明: 函数返回字符s[i], s[i+1], ···, s[j]的内部数字编码(ASCII码)，其中参数i的默认值是1，而参数j的默认值是i
```

**将整型数字转成字符并连接**
```lua
语法: string.char(arg)
功能说明: 将整型数字转成字符并连接
```

**字符串中查找子字符串**
```lua
语法: string.find(s, pattern [, init [, plain]])
功能说明: 函数在字符串s里查找第一个和参数pattern匹配的子串，如果找到了一个匹配的子串，
就会返回这个子串的起始索引和结束索引，否则就会返回nil。另外，参数init作为一个数字，指定了搜索的起始位置，
这个数字默认为1可以一个负数，表示从后往前数的字符个数。参数plain作为第四个可选参数默认为flase，
传入参数true表示关闭模式匹配，所以函数只做简单的查找子串的操作，如果子串pattern没有字符为空字符串""将会被认为是魔法字符。
如果模式匹配子串被找到了，一个成功被找到的子串将会作为第三个返回值，放在两个索引返回值的后边而返回。
返回其具体位置。不存在则返回 nil。
```

**字符格式化**
```lua
语法: string.format(...)
功能说明: 字符串格式化输出
```

**字符串长度**
```lua
语法: string.len(arg)
功能说明: 获取字符串长度
```

**转为小写字母**
```lua
语法: string.lower(argument)
功能说明: 字符串全部转为小写字母
```

**返回字符串string的n个拷贝**
```lua
语法: string.rep(string,n)
功能说明: 返回字符串string的n个拷贝
```

**字符串反转**
```lua
语法: string.reverse(argument)
功能说明: 将一个字符串反转
```

**转为大写字母**
```lua
语法: string.upper(argument)
功能说明: 字符串全部转为大写字母
```

### UTF8字符串方法

**查找utf8字符串**
```lua
语法: utf8.inStr(start,string1,string2[,comparision])
参数说明:
  start: 数值表达式,用于设置每次搜索的开始位置
  string1: 要搜索的字符串表达式
  string2: 被搜索的字符串表达式
  comparision: 对比方式,是否忽略大小写,可省略,默认false,即不忽略大小写
返回值: 字符串在另一字符串中第一次出现的位置
```

**反向查找utf8字符串**
```lua
语法: utf8.inStrRev(string1,string2,start,compare)
参数说明:
  string1: 被搜索的字符串任意有效字符串表达式
  string2: 要搜索的字符串任意有效字符串表达式
  start: 开始搜索的字符位置,-1表示从最后一个字符开始搜索
  compare: 可选。是否忽略大小写,默认False,即不忽略大小写
返回值: 数值型,返回反方向搜索字符串在原字符串中第一次出现的正方向位置
```

**取左边字符**
```lua
语法: utf8.left(string1,len)
参数说明:
  string1: utf8字符串
  len: 要取的长度
返回值: utf8字符串取左边字符
```

**获取utf8字符串长度**
```lua
语法: utf8.length(string1)
参数说明:
  string1: utf8字符串
返回值: utf8字符串长度
```

**从字符串的指定位置开始截取一定数量的字符**
```lua
语法: utf8.mid(string,start,length)
参数说明:
  string: utf8字符串
  start: 起点位置
  length: 要取的长度
返回值: 从utf8字符串中返回指定数目的字符
```

**取右边字符**
```lua
语法: utf8.right(string1,len)
参数说明:
  string1: utf8字符串
  len: 要取的长度
返回值: utf8字符串取右边字符
```

**移除指定数目字符**
```lua
语法: utf8.strCut(string,start,length)
参数说明:
  string: utf8字符串
  start: 起点位置
  length: 要移除的长度
返回值: 字符串型，返回移除了指定字符后的字符串
```

**颠倒utf8字符串**
```lua
语法: utf8.strReverse(string1)
参数说明:
  string1: 要进行字符反向的字符串。如果string1是零长度字符串("")
返回值: 与指定字符串顺序相反的字符串
```

## 设备方法

### 系统环境和权限

**java层设置脚本结束回调函数**
```lua
语法: LuaEngine.registerExitCallback(callback)
```

**设置一个图片代替系统自带的截图，后面的图色全部基于这个图片**
```lua
语法: setSnapCacheBitmap(bmp)
参数: bmp - 一个java Bitmap对象，当bmp是空的时候切换回系统截图
```

**判断当前是否是调试状态**
```lua
语法: checkIsDebug()
```

**以当前最高权限执行命令**
```lua
语法: exec(cmd,[isRet])
参数说明: cmd - 执行命令字符串, isRet - 布尔类型表示是否返回执行的结果，默认是true
```

**停止脚本运行**
```lua
语法: exitScript()
```

**重启当前脚本**
```lua
语法: restartScript()
```

**获取当前运行环境类型**
```lua
语法: getRunEnvType()
返回值: 整数类型0为root类型或者激活类型 1为无障碍类型
```

**设置无障碍引擎运行模式**
```lua
语法: setAccessibilityEnvMode()
说明: 设置后必须重启脚本下次启动才会生效
```

**设置手动引擎模式**
```lua
语法: setHandleEnvMode()
说明: 设置后必须重启脚本下次启动才会生效
```

**设置引擎模式为ROOT(激活)模式**
```lua
语法: setRootEnvMode(mode)
参数: mode - 布尔类型，如果为true表示脚本引擎运行在最高权限中
说明: 设置后必须重启脚本下次启动才会生效
```

### 应用管理

**判断当前app是否为前台运行**
```lua
语法: appIsFront(package_name)
参数说明: package_name - 指定要查找的包名
```

**判断当前app是否在运行中**
```lua
语法: appIsRunning(package_name)
参数说明: package_name - 指定要查找的包名
```

**获取前台app包名**
```lua
语法: frontAppName()
参数说明: 无
返回值: 前台app包名
```

**获取最顶层activity名称**
```lua
语法: getCurrentActivity()
说明: 此函数需要开启节点服务或者无障碍服务
参数说明: 无
返回值: 获取最顶层activity名称
```

**获取当前包名**
```lua
语法: getPackageName()
```

**获取所有安装的app的详细信息**
```lua
语法: getInsallAppInfos()
```

**获取已安装apk**
```lua
语法: getInstalledApk()
```

**获取已安装apk的信息**
```lua
语法: getInstalledApps()
```

**打开app**
```lua
语法: runApp(package_name,[component_name],[is_open_by_super])
参数说明:
  package_name: 字符串型,某个应用程序的包名
  component_name: 可选参数,字符串型,某个应用程序的组件名,可省略
  is_open_by_super: 可选参数,布尔类型,表示是否用最高权限去打开
返回值: 空
```

**关闭app**
```lua
语法: stopApp(package_name)
参数说明: package_name - 字符串型,某个应用程序的包名
返回值: 空
```

**安装apk**
```lua
语法: installApk(apkpath)
参数说明: apkpath - 待安装的apk的绝对路径
```

**安装更新包lr文件**
```lua
语法: installLrPkg(lrpath)
参数说明: lrpath - 待安装的更新包文件绝对路径
```

### 设备信息获取

**获取壳子版本**
```lua
语法: getApkVerInt()
功能说明: 获取当前apk壳子的版本号，返回一个整数
```

**获取设备电量**
```lua
语法: getBatteryLevel()
参数说明: 无
```

**获取主板号**
```lua
语法: getBoard()
```

**获取BOOTLOADER版本号**
```lua
语法: getBootLoader()
```

**获取设备BRAD号**
```lua
语法: getBrand()
```

**cpu支持类型**
```lua
语法: getCpuAbi()
```

**cpu支持类型**
```lua
语法: getCpuAbi2()
```

**获取当前cpu架构**
```lua
语法: getCpuArch()
返回值: 返回1表示arm架构0是x86架构
```

**获取设备别名**
```lua
语法: getDevice()
```

**获取设备id号**
```lua
语法: getDeviceId()
功能说明: 获取设备id号
```

**获取设备dpi**
```lua
语法: getDisplayDpi()
参数说明: 无
```

**获取设备的分辨率详细信息**
```lua
语法: getDisplayInfo()
```

**屏幕旋转方向**
```lua
语法: getDisplayRotate()
参数说明: 无
返回值: 0无旋转 1表示屏幕逆时针旋转90度 2表示屏幕逆时针旋转180度 3表示屏幕逆时针旋转270度
```

**获取屏幕分辨率**
```lua
语法: getDisplaySize()
参数说明: 无
返回值: 返回屏幕的尺寸长和宽
```

**获取编译版本号**
```lua
语法: getFingerprint()
```

**获取硬件序列号**
```lua
语法: getHardware()
```

**获取id修订号**
```lua
语法: getId()
```

**获取制造商代号**
```lua
语法: getManufacturer()
```

**获取型号**
```lua
语法: getModel()
```

**获取设备的oaid**
```lua
语法: getOaid()
```

**获取产品代号**
```lua
语法: getProduct()
```

**获取sd卡的绝对路径**
```lua
语法: getSdPath()
示例: local path = getSdPath(); print("sd卡路径:"..path)
```

**获取sdk版本**
```lua
语法: getSdkVersion()
```

**获取所有传感器信息**
```lua
语法: getSensorsInfo()
```

**获取设备sim卡序列号**
```lua
语法: getSimSerialNumber()
参数说明: 无
```

**获取设备imsi**
```lua
语法: getSubscriberId()
参数说明: 无
```

**获取wifi mac地址**
```lua
语法: getWifiMac()
```

**获取当前脚本的工作目录**
```lua
语法: getWorkDir()
功能说明: 获取当前脚本的工作目录，该目录有可执行权限
```

### 硬件控制

**保持屏幕长亮**
```lua
语法: lockScreen()
功能说明: 保持屏幕不熄灭，一直长亮
```

**释放屏幕长亮**
```lua
语法: unLockScreen()
```

**设置息屏运行**
```lua
语法: setDisplayPowerOff(isPoweroff)
说明: 此方法需要root或者激活权限
参数说明: isPoweroff - 布尔类型，true表示息屏运行false表示恢复正常
```

**恢复原始设备的分辨率和dpi**
```lua
语法: setDpiToRealy()
说明: 此方法只有在打包的apk里面设置了虚拟分辨率才有效，仅root或者激活模式能使用
```

**修改当前屏幕分辨率为虚拟屏幕的分辨率**
```lua
语法: setDpiToVir(dpi)
说明: 此方法只有在打包的apk里面设置了虚拟分辨率才有效，仅root或者激活模式能使用
参数说明: dpi - 整数类型，设备的dpi
返回值: 布尔类型
```

**打开或关闭飞行模式**
```lua
语法: setAirplaneMode(state)
参数说明: state - 为true或false,为真是打开否则关闭飞行模式
```

**打开或关闭蓝牙**
```lua
语法: setBTEnable(state)
参数说明: state - 为true或false,为真是打开否则关闭蓝牙
```

**打开或关闭wifi**
```lua
语法: setWifiEnable(state)
参数说明: state - 为true或false,为真是打开否则关闭wifi
```

**震动**
```lua
语法: vibrate(during)
参数说明: during - 震动时间,单位是毫秒
功能说明: 震动设备
```

### 通信功能

**拨号或者打电话**
```lua
语法: phoneCall(number,state)
参数说明: number - 要拨打的电话号码, state - 为0则拨号否则打出
```

**发送短信**
```lua
语法: sendSms(number,content)
参数说明: number - 要发送短信的电话号码, content - 短信内容
```

### 剪贴板操作

**读取剪贴板**
```lua
语法: readPasteboard()
参数说明: 无
返回值: 字符串类型,返回剪贴板内容
```

**写数据到剪贴板**
```lua
语法: writePasteboard(str)
参数说明: str - 字符串数据写入剪贴板
返回值: 无
```

### 文件资源操作

**释放资源到指定目录**
```lua
语法: extractApkAssets(res,outdir)
参数说明: res - 当前打包到apk里面的资源文件, outdir - 要释放文件的存放路径
```

**释放资源到指定目录**
```lua
语法: extractAssets(assets,outdir,pattern)
参数说明: assets - 当前项目中的资源文件名称, outdir - 要释放文件的存放路径, pattern - 匹配模式可以是模糊匹配也可以是明确的文件名称
```

**解压zip文件到指定的目录**
```lua
语法: unZip(zippath,outdir,pass,charset)
参数说明: 
  zippath - 压缩包的路径
  outdir - 要释放文件的存放路径
  pass - 字符类型，解压密码，没有密码可以不填写或者空字符串
  charset - 字符串类型，一般有两种GBK和UTF-8 window上基本上都是GBK压缩
```

**zip 压缩文件或者文件夹**
```lua
语法: zip(file,zippath)
参数说明: file - 待压缩的文件或者文件夹, zippath - 压缩文件绝对路径
返回值: 布尔类型
```

**刷新图片文件到图库**
```lua
语法: scanImage(imagepath)
参数说明: imagepath - 图片的绝对路径
```

### 多媒体操作

**播放音乐**
```lua
语法: playAudio(audio_path)
参数说明: audio_path - 声音资源文件名称
```

**关闭播放音乐**
```lua
语法: stopAudio()
参数说明: 无
```

### 意图和事件

**打开一个意图**
```lua
语法: runIntent(intent)
说明: 部分手机需要开启允许后台弹窗才能正常运行
参数说明: intent - 意图，为一个表类型 intent = {}; 其中包含 action,uri,packageName,data，extra
返回值: 布尔类型
```

**向插件发送自定义数据**
```lua
语法: sendPluginEvent(what,arg)
参数: what - 整数类型是自定义事件类型，arg - 字符串类型自定义数据
示例: sendPluginEvent(1,"我是脚本,你好")
```

### 回调和配置

**读取UI配置**
```lua
语法: getUIConfig(configName)
参数说明: configName - ui配置文件名称
返回值: result - 数组类型返回用户的配置键值对
```

**设置系统通知事件回调**
```lua
语法: setPluginEventCallBack(cb)
说明: 回调函数接受两个参数第一个是类型第二个是结果，类型为1表示状态栏消息通知
参数: cb - 回调函数
```

**设置插件回调函数**
```lua
语法: setPluginEventCallBack(cb)
参数: cb - 回调函数
功能说明: 插件通过PluginService实例的sendScriptEvent发送的自定义数据
```

**设置停止脚本的回调函数**
```lua
语法: setStopCallBack()
功能说明: 如果想在脚本结束的时候能处理一些收尾的事情可以用此方法设置结束时的回调方法
注意: 使用此方法的回调函数中不能使用ui显示操作
```

**设置悬浮按钮菜单自定义事件回调函数**
```lua
语法: setUserEventCallBack(cb)
说明: 注意只有新版悬浮菜单才能触发此回调函数
参数: cb - 回调函数
功能说明: 用户通过点击悬浮按钮菜单来触发一个事件给脚本
```

### 悬浮窗控制

**设置悬浮窗位置**
```lua
语法: setControlBarPosNew(x,y)
参数说明: x,y - 小数类型取值范围(0-1.0) 表示占用的长宽比
功能说明: 设置悬浮窗位置
```

**显示或者隐藏悬浮按钮**
```lua
语法: showControlBar(isShow)
参数说明: isShow - 如果是true则显示否则隐藏
```

### 系统设置

**设置截图缓存时间**
```lua
语法: setSnapCacheTime(time)
参数: time - 设置截图缓存时间系统默认缓存是100毫秒
示例: setSnapCacheTime(0)
```

**关闭日志开关**
```lua
语法: setLogOff(iscloselog)
参数说明: iscloselog - 布尔类型true表示关闭日志输出否则打开
```

**设置主线程阻塞**
```lua
语法: setMainThreadPause()
参数说明: 无
返回值: 布尔类型
```

**设置主线程恢复继续运行**
```lua
语法: setMainThreadResume()
参数说明: 无
返回值: 布尔类型
```

### 定时器和工具

**定时执行指定的方法**
```lua
语法: setTimer(function,time,arg,...)
参数说明: function - 需要调用的函数名称, time - 当前时间到调用函数的时间间隔(毫秒), arg - 参数列表
```

**生成随机数**
```lua
语法: rnd(begin,end)
参数说明: begin,end - 开始数和结束数必须是整数
```

**休眠**
```lua
语法: sleep(time)
参数说明: time - 休眠的时间单位是毫秒
```

**调试输出**
```lua
语法: print(str)
参数说明: str - 调试打印输出信息
```

**调试输出**
```lua
语法: printEx(str)
参数说明: str - 调试打印输出信息
```

**获取懒人开放的api接口**
```lua
语法: getLrApi()
说明: 返回的是一个结构体指针具体使用看压缩包里面的ffi测试例子
之前的lrGetScreenPixel 和 lrRelease 将不在支持导出但是可以通过这个接口获取到
```

# 图色方法

## 截图相关

### LuaEngine.snapShot

**Java层截图方法**

```lua
LuaEngine.snapShot(l,t,r,b)
```

**参数说明**

- `l,t,r,b` - 表示范围

**返回值**

- 返回一个java的Bitmap对象,记得一定要释放避免内存泄漏

------

### keepCapture

**截图到内存**

```lua
keepCapture()
```

**功能说明**

- 截取当前屏幕内容保留在内存,可以供后续图色查找

------

### releaseScreen

**删除内存中的截图**

```lua
releaseScreen()
```

**功能说明**

- 删除内存中的截图

------

### snapShot

**截图并保存**

```lua
snapShot(path,[left,top,right,bottom])
```

**参数说明**

- `path` - 截图保存路径
- `left,top,right,bottom` - 可选的截屏区域

------

## 飞桨OCR识别

### PaddleOcr.detect

**根据模型识别图片**

```lua
PaddleOcr.detect(left,top,right,bottom)
```

**参数说明**

- `left,top,right,bottom` - 屏幕范围

**返回值**

- json字符串

**官方模型地址**

- https://github.com/PaddlePaddle/PaddleOCR/tree/release/2.7

------

### PaddleOcr.detectWithPadding

**根据模型识别图片增强版**

```lua
PaddleOcr.detectWithPadding(bmp,padding,r,g,b)
```

**参数说明**

- `bmp` - 一个Bitmap类型的位图
- `padding` - 期望目标图标周围填充空白大小单位是像素
- `r,g,b` - 空白区域的颜色,分别是通道红、绿、蓝

**返回值**

- json字符串

**功能说明**

- 通过给图片周围添加空白增强识别率

**官方模型地址**

- https://github.com/PaddlePaddle/PaddleOCR/tree/release/2.7

------

### PaddleOcr.loadModel

**加载自带的模型**

```lua
PaddleOcr.loadModel(isUseOnnxModel)
```

**参数说明**

- `isUseOnnxModel` - 布尔类型,懒人目前支持ncnn和onnx两种模型,isUseOnnxModel为true表示加载默认自带的onnx飞桨模型,为false表示用转换过的ncnn飞桨模型

**返回值**

- 布尔类型

**注意事项**

- onnx模型精度最高(默认模型是使用飞桨官方的PP-OCRv4模型)

------

### PaddleOcr.loadNnccModel

**加载自己训练的ncnn模型**

```lua
PaddleOcr.loadNnccModel(detParams,recParams,detBin,recBin,keyTxt)
```

**参数说明**

- `detParams` - 检测模型算子描述文件绝对路径
- `recParams` - 文本模型算子描述文件绝对路径
- `detBin` - 检测模型文件绝对路径
- `recBin` - 文本识别模型绝对路径
- `keyTxt` - 训练的文本集绝对路径

**返回值**

- json字符串

**官方模型地址**

- https://github.com/PaddlePaddle/PaddleOCR/tree/release/2.7

------

### PaddleOcr.loadOnnxModel

**加载自己训练的onnx模型**

```lua
PaddleOcr.loadOnnxModel(modelDetPath,modelClsPath,modelRecPath,keyTxt)
```

**参数说明**

- `modelDetPath` - 检测模型绝对路径
- `modelClsPath` - 方向分类器模型路径
- `modelRecPath` - 文本识别模型路径
- `keyTxt` - 训练的文本集绝对路径

**返回值**

- json字符串

**官方模型地址**

- https://github.com/PaddlePaddle/PaddleOCR/tree/release/2.7

------

## YoloV5目标检测

### YoloV5.detect

**yolov5目标检测**

```lua
YoloV5.detect(bmp,usegpu)
```

**参数说明**

- `bmp` - Bitmap对象
- `usegpu` - 是否使用GPU

**返回值**

- 获取一个结果集

------

## 颜色比对

### cmpColor

**指定坐标颜色比对**

```lua
cmpColor(x,y,color,sim)
```

**参数说明**

- `x` - 整数型,需要对比颜色的X坐标
- `y` - 整数型,需要对比颜色的Y坐标
- `color` - 字符串,待比较的16进制颜色,格式为"BBGGRR",多个颜色用"|"号分隔,支持偏色,比如"787878-101010|123456"
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 整型,1为匹配到,0为没有匹配到

------

### cmpColorEx

**同时比较指定的多个坐标点的颜色**

```lua
cmpColorEx(mul_color,sim)
```

**参数说明**

- `mul_color` - 字符串,需要对比的点xy坐标和16进制颜色,格式为(X坐标|Y坐标|16进制颜色),多个颜色用"|"号分隔,需要偏色用"-"号分隔,多个点信息用","号分隔,比如"100|200|FFFFFF|123456-000000,300|500|FFFFFF"
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 整型,1为完全匹配,0为没有完全匹配到

**功能说明**

- 支持多色、偏色、相似度比较

------

### cmpColorExT

**同时比较指定的多个坐标点的颜色(数组参数版)**

```lua
cmpColorExT({mul_color,sim})
```

**参数说明**

- 数组的各项内容如下:
  - `mul_color` - 字符串,需要对比的点xy坐标和16进制颜色,格式为(X坐标|Y坐标|16进制颜色),多个颜色用"|"号分隔,需要偏色用"-"号分隔,多个点信息用","号分隔,比如"100|200|FFFFFF|123456-000000,300|500|FFFFFF"
  - `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 整型,1为完全匹配,0为没有完全匹配到

**功能说明**

- 此功能和cmpColorEx一样不过参数是一个数组格式,提高脚本开发效率

------

### colorDiff

**颜色比对**

```lua
colorDiff(color1,color2)
```

**参数说明**

- `color1` - 16进制颜色,格式为"BBGGRR",或者10进制颜色
- `color2` - 16进制颜色,格式为"BBGGRR",或者10进制颜色

**返回值**

- 整数型:两个颜色的红、绿、蓝三个分量的差值之总和

------

### colorToRGB

**颜色转RGB**

```lua
colorToRGB(color)
```

**参数说明**

- `color` - 16进制颜色,格式为"BBGGRR",或者10进制颜色

**返回值**

- `r` - 整数型,十进制颜色红分量
- `g` - 整数型,十进制颜色绿分量
- `b` - 整数型,十进制颜色蓝分量

------

## OCR识别

### createOcr

**创建一个tresseract-ocr(4.1.0)本地光学ocr对象**

```lua
createOcr(lang,[mode],[engine])
```

**参数说明**

- `lang` - 字符串型,字库语言文件名称,不填则默认eng(只支持一般的中英+数字+标点符号),注意lang也可以是字库文件的绝对路径,如果字库比较大,那么建议先把字库放到手机sd卡下测试,不然如果放到资源文件中,数据包会很大导致调试速度很慢

- ```
  mode
  ```

   \- 整数型

  - 0 仅检测方向和文本
  - 1 自动检测方向和文本(OSD)
  - 2 自动检测,但不进行OSD或OCR处理
  - 3 自动PSM模式(但不含OSD)
  - 4 所识别图片的字体大小不一
  - 5 所识别图片当作整块竖向文字区域
  - 6 所识别图片当作整块横向文字区域 (默认值)
  - 7 所识别图片当作一行文字
  - 8 所识别图片当作单个词语
  - 9 所识别图片当作单个圆型围绕的词语
  - 10 所识别图片当作单个英文/数字字符
  - 11 尽可能识别更多的字符(无顺序要求)
  - 12 分散稀疏的OSD检测

- ```
  engine
  ```

   \- 整数型

  - 0 OEM_TESSERACT_ONLY 老版本训练引擎
  - 1 OEM_LSTM_ONLY 新版本基于神经网络的训练引擎
  - 3 OEM_DEFAULT 默认引擎

**返回值**

- 一个tresseract-ocr对象句柄

**说明**

- 请下载群里的光学ocr训练工具可以自己针对性的训练自己的字库

------

## 图形查找

### findCircle

**霍夫变换找圆**

```lua
findCircle(x1, y1, x2, y2,dp,minDist,param1,param2,minRadius,maxRadius)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `dp` - 累加面分辨率
- `minDist` - 两个圆心之间的最小距离
- `param1` - Canny 边缘检测的高阈值,低阈值被自动置为高阈值的一半,默认为 100
- `param2` - 累加平面某点是否是圆心的判定阈值。它越大,能通过检测的圆就更接近完美的圆形,默认为 100
- `minRadius` - 圆半径的最小值.默认为 0
- `maxRadius` - 圆半径的最大值,默认为 0

**返回值**

- `ret` - 数组类型{{x=centerX,y=centerY,r=radius},{x=centerX1,y=centerY1,r=radius1}}

------

### findColor

**在指定区域内查找指定的颜色**

```lua
findColor(x1, y1, x2, y2,color,dir,sim)
```

**参数说明**

- `x1` - 整数型,区域左上角x坐标

- `y1` - 整数型,区域左上角y坐标

- `x2` - 整数型,区域右下角x坐标

- `y2` - 整数型,区域右下角y坐标

- `color` - 字符串:要对比的16进制颜色,格式为"BBGGRR"多个颜色用"|"号分隔,如果需要对比偏色则中间用"-"号分隔,比如"FFFFFF|123456-000000|00FF00-101010"

- ```
  dir
  ```

   \- 整数型,查找方向

  - 0: 表示从左上向右下
  - 1: 表示从中心往四周查找
  - 2: 表示从右下向左上查找
  - 3: 表示从左下向右上查找
  - 4: 表示从右上向左下查找

- `sim` - 双精度浮点数:相似度,取值范围0-1

**返回值**

- `ret` - 如果有多个颜色用"|"分割,则返回结果为对应找到的索引
- `x,y` - 对应找到的坐标

------

### findColorT

**在指定区域内查找指定的颜色(数组参数版)**

```lua
findColorT({x1, y1, x2, y2,color,dir,sim})
```

**参数说明**

- 参数是一个数组类型,各项如下:

  - `x1` - 整数型,区域左上角x坐标

  - `y1` - 整数型,区域左上角y坐标

  - `x2` - 整数型,区域右下角x坐标

  - `y2` - 整数型,区域右下角y坐标

  - `color` - 字符串:要对比的16进制颜色,格式为"BBGGRR"多个颜色用"|"号分隔,如果需要对比偏色则中间用"-"号分隔,比如"FFFFFF|123456-000000|00FF00-101010"

  - ```
    dir
    ```

     \- 整数型,查找方向

    - 0: 表示从左上向右下
    - 1: 表示从中心往四周查找
    - 2: 表示从右下向左上查找
    - 3: 表示从左下向右上查找
    - 4: 表示从右上向左下查找

  - `sim` - 双精度浮点数:相似度,取值范围0-1

**返回值**

- `ret` - 如果有多个颜色用"|"分割,则对应找到的索引
- `x,y` - 对应找到的坐标

**功能说明**

- 功能和findColor一样不过参数是一个数组,这样做的目的是为了提高开发效率

------

### findImage

**opencv模板匹配找图**

```lua
findImage(x1, y1, x2, y2,pic_name,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `pic_name` - 字符串,要找的图片名字,多个图片用"|"号分隔
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- `ret` - 如果有多个颜色用"|"分割,则对应找到的索引
- `x,y` - 对应找到的坐标

------

### findMultiColor

**区域多点找色**

```lua
findMultiColor(x1,y1,x2,y2,first_color,offset_color,dir,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标

- `y1` - 整数型,查找区域左上Y坐标

- `x2` - 整数型,查找区域右下X坐标

- `y2` - 整数型,查找区域右下Y坐标

- `first_color` - 要对比的16进制颜色,多个颜色用"|"号分隔,如果需要对比偏色则中间用"-"号分隔,比如"888888|123456-000000|00FF00-101010"

- `offset_color` - 字符串,偏移颜色

- ```
  dir
  ```

   \- 整数型,查找方向

  - 0:表示从左上向右下查找
  - 1:表示从中心往四周查找
  - 2:表示从右下向左上查找
  - 3:表示从左下向右上查找
  - 4:表示从右上向左下查找

- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 如果成功返回对应点的坐标x,y,否则返回-1,-1

------

### findMultiColorAll

**区域多点找色(查找所有)**

```lua
findMultiColorAll(x1,y1,x2,y2,first_color,offset_color,dir,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标

- `y1` - 整数型,查找区域左上Y坐标

- `x2` - 整数型,查找区域右下X坐标

- `y2` - 整数型,查找区域右下Y坐标

- `first_color` - 要对比的16进制颜色,多个颜色用"|"号分隔,如果需要对比偏色则中间用"-"号分隔,比如"888888|123456-000000|00FF00-101010"

- `offset_color` - 字符串,偏移颜色

- ```
  dir
  ```

   \- 整数型,查找方向

  - 0:表示从左上向右下查找
  - 1:表示从中心往四周查找
  - 2:表示从右下向左上查找
  - 3:表示从左下向右上查找
  - 4:表示从右上向左下查找

- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 如果成功返回所有满足要求的表格数组

------

### findMultiColorAllT

**区域多点找色(查找所有)(数组参数版)**

```lua
findMultiColorAllT({x1,y1,x2,y2,first_color,offset_color,dir,sim})
```

**参数说明**

- 传递是的是一个数组,数组的每一项如下:

  - `x1` - 整数型,查找区域左上X坐标

  - `y1` - 整数型,查找区域左上Y坐标

  - `x2` - 整数型,查找区域右下X坐标

  - `y2` - 整数型,查找区域右下Y坐标

  - `first_color` - 要对比的16进制颜色,多个颜色用"|"号分隔,如果需要对比偏色则中间用"-"号分隔,比如"888888|123456-000000|00FF00-101010"

  - `offset_color` - 字符串,偏移颜色

  - ```
    dir
    ```

     \- 整数型,查找方向

    - 0:表示从左上向右下查找
    - 1:表示从中心往四周查找
    - 2:表示从右下向左上查找
    - 3:表示从左下向右上查找
    - 4:表示从右上向左下查找

  - `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 如果成功返回所有满足要求的表格数组

**功能说明**

- 和findMultiColorAll一样不过参数是一个数组,这样做的目的是为了提高开发效率

------

### findMultiColorT

**区域多点找色(数组参数版)**

```lua
findMultiColorT({x1,y1,x2,y2,first_color,offset_color,dir,sim})
```

**参数说明**

- 传递是的是一个数组,数组的每一项如下:

  - `x1` - 整数型,查找区域左上X坐标

  - `y1` - 整数型,查找区域左上Y坐标

  - `x2` - 整数型,查找区域右下X坐标

  - `y2` - 整数型,查找区域右下Y坐标

  - `first_color` - 要对比的16进制颜色,多个颜色用"|"号分隔,如果需要对比偏色则中间用"-"号分隔,比如"888888|123456-000000|00FF00-101010"

  - `offset_color` - 字符串,偏移颜色

  - ```
    dir
    ```

     \- 整数型,查找方向

    - 0:表示从左上向右下查找
    - 1:表示从中心往四周查找
    - 2:表示从右下向左上查找
    - 3:表示从左下向右上查找
    - 4:表示从右上向左下查找

  - `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 如果成功返回对应点的坐标x,y,否则返回-1,-1

**功能说明**

- 和findMultiColor一样不过参数是一个数组,这样做的目的是为了提高开发效率

------

### findPic

**区域找图**

```lua
findPic(x1, y1, x2, y2,pic_name,delta_color,dir,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标

- `y1` - 整数型,查找区域左上Y坐标

- `x2` - 整数型,查找区域右下X坐标

- `y2` - 整数型,查找区域右下Y坐标

- `pic_name` - 字符串,要找的图片名字,多个图片用"|"号分隔

- `delta_color` - 16进制字符串,偏色

- ```
  dir
  ```

   \- 整数型,查找方向:

  - 0:表示从左上向右下查找
  - 1:表示从中心往四周查找
  - 2:表示从右下向左上查找
  - 3:表示从左下向右上查找
  - 4:表示从右上向左下查找

- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- `ret` - 如果有多个颜色用"|"分割,则对应找到的索引
- `x,y` - 对应找到的坐标

------

### findPicAllPoint

**图片查找获取要查找图片在所选区域中的所有坐标**

```lua
findPicAllPoint(x1, y1, x2, y2,pic_name,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `pic_name` - 字符串,要找的图片名字,注意这里是单个图片的查找
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- `ret` - 返回一个坐标数组

------

### findPicEx

**高级区域找图**

```lua
findPicEx(x1, y1, x2, y2,pic_name,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `pic_name` - 字符串,要找的图片名字,多个图片用"|"号分隔
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- `ret` - 如果有多个颜色用"|"分割,则对应找到的索引
- `x,y` - 对应找到的坐标

------

### findPicFast

**快速区域找图**

```lua
findPicFast(x1, y1, x2, y2,pic_name,delta_color,dir,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标

- `y1` - 整数型,查找区域左上Y坐标

- `x2` - 整数型,查找区域右下X坐标

- `y2` - 整数型,查找区域右下Y坐标

- `pic_name` - 字符串,要找的图片名字,多个图片用"|"号分隔

- `delta_color` - 16进制字符串,偏色

- ```
  dir
  ```

   \- 整数型,查找方向:

  - 0:表示从左上向右下查找
  - 1:表示从中心往四周查找
  - 2:表示从右下向左上查找
  - 3:表示从左下向右上查找
  - 4:表示从右上向左下查找

- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- `index` - 如果有多个颜色用"|"分割,则对应找到的索引
- `retList` - 所有找到的坐标数组列表

**注意事项**

- 暂时不支持透明图

------

## 文字识别

### findStr

**区域文字查找**

```lua
findStr(x1, y1, x2, y2,text,colorfmt,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `text` - 字符串,要找的文字,多个图片用"|"号分隔
- `colorfmt` - 字符串,文字的颜色格式
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- `ret` - 如果有多个颜色用"|"分割,则对应找到的索引
- `x,y` - 对应找到的坐标

------

### findStrEx

**区域文字查找增强版**

```lua
findStrEx(x1, y1, x2, y2,text,colorfmt,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `text` - 字符串,要找的文字,多个图片用"|"号分隔
- `colorfmt` - 字符串,文字的颜色格式
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- `ret` - 返回一个json数组字符串

------

### findStrExNew

**区域文字查找增强版(指定字库)**

```lua
findStrExNew(index,x1, y1, x2, y2,text,colorfmt,sim)
```

**参数说明**

- `index` - 字库的索引
- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `text` - 字符串,要找的文字,多个图片用"|"号分隔
- `colorfmt` - 字符串,文字的颜色格式
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- `ret` - 返回一个json数组字符串

------

## 颜色统计

### getColorNum

**获取区域颜色数量**

```lua
getColorNum(x1,y1,x2,y2,color,sim)
```

**参数说明**

- `x1` - 整数型,区域左上X坐标
- `y1` - 整数型,区域左上Y坐标
- `x2` - 整数型,区域右下X坐标
- `y2` - 整数型,区域右下Y坐标
- `color` - 字符串,要找的16进制颜色,格式为"BBGGRR",多个颜色用"|"号分隔,支持偏色,比如"FFFFFF-000000|123456"
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 整数型,指定区域颜色匹配相同的像素点个数

**功能说明**

- 支持多色、偏色、相似度比较

------

### getPixelColor

**获取指定坐标处屏幕的颜色值**

```lua
getPixelColor(x,y,[type])
```

**参数说明**

- `x,y` - 屏幕坐标
- `type` - 可选参数,type=1时返回一个整数,默认不填写type是返回颜色的16进制字符串

**返回值**

- 返回指定坐标处的颜色值

------

### getScreenPixel

**获取指定范围内的像素数组**

```lua
getScreenPixel(x1,y1,x2,y2)
```

**参数说明**

- `x1,y1,x2,y2` - 获取范围坐标,当全部为0表示获取全屏像素值

**返回值**

- `w,h,arr` - 其中w,h是获取范围的长宽,arr是返回的数组,如果w,h小于0则表明失败

------

## 屏幕变化检测

### isDisplayDead

**检测屏幕区域所有色点是否变化**

```lua
isDisplayDead(x1, y1, x2, y2,time)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `time` - 整数型,等待的时间单位是秒

**返回值**

- `ret` - true表示没有变化,false有变化

**说明**

- 该方法会阻塞住,等待颜色发生改变,区域内颜色一旦发生变化立刻返回

------

## OCR文字识别

### ocr

**根据选中的字典查找该区域内所有文字组成的字符串**

```lua
ocr(x1, y1, x2, y2,colorfmt,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `colorfmt` - 字符串,文字的颜色格式
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 字符串

------

### ocrBinaryEx

**二值化本地神经网络OCR**

```lua
ocrBinaryEx(x1, y1, x2, y2,color_desc,[padding],[maxSlidLen],[boxscoreth],[boxthresh],[clipratio],[doAngle],[mostAngle])
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `color_desc` - 字符串,文字的颜色格式
- `padding` - 整数型,周围添加隔离空白区域大小,默认不写是50
- `maxSlidLen` - 整数型,长宽按比例缩放最大值,默认不写为0
- `boxscoreth` - 浮点类型,框置信度默认不写是0.6
- `boxthresh` - 浮点类型,默认0.3
- `clipratio` - 框大小倍率,默认2.0
- `doAngle` - 布尔类型,是否角度分析默认为true
- `mostAngle` - 布尔类型,是否进行方向分析默认为true

**返回值**

- 数组类型

**说明**

- 这些参数可以通过群里的神经网络ocr.exe工具进行调试到自己满意值后直接生成代码

------

### ocrEx

**本地神经网络OCR**

```lua
ocrEx(x1, y1, x2, y2,[padding],[maxSlidLen],[boxscoreth],[boxthresh],[clipratio],[doAngle],[mostAngle])
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `padding` - 整数型,周围添加隔离空白区域大小,默认不写是50
- `maxSlidLen` - 整数型,长宽按比例缩放最大值,默认不写为0
- `boxscoreth` - 浮点类型,框置信度默认不写是0.6
- `boxthresh` - 浮点类型,默认0.3
- `clipratio` - 框大小倍率,默认2.0
- `doAngle` - 布尔类型,是否角度分析默认为true
- `mostAngle` - 布尔类型,是否进行方向分析默认为true

**返回值**

- 数组类型

**说明**

- 这些参数可以通过群里的神经网络ocr.exe工具进行调试到自己满意值后直接生成代码

------

### ocrNew

**根据选中的字典查找该区域内所有文字组成的字符串(指定字库)**

```lua
ocrNew(index,x1, y1, x2, y2,colorfmt,sim)
```

**参数说明**

- `index` - 字库的索引
- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `colorfmt` - 字符串,文字的颜色格式
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- 字符串

------

### ocrText

**获取指定范围内的文本字符串**

```lua
ocrText(handle,x1,y1,x2,y2,colorfmt)
```

**参数说明**

- `handle` - 整数型,tresseract-ocr对象句柄
- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `colorfmt` - 字符串,文字的颜色格式

**返回值**

- 返回查找的字符串

------

### ocrTextEx

**获取指定范围内的文本字符串增强版**

```lua
ocrTextEx(handle,x1,y1,x2,y2,[ThresholdTypes],[thresh],[maxthresh])
```

**参数说明**

- `handle` - 整数型,tresseract-ocr对象句柄
- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `ThresholdTypes` - 整数型,(THRESH_BINARY = 0,THRESH_BINARY_INV = 1,THRESH_TRUNC = 2,THRESH_TOZERO = 3,THRESH_TOZERO_INV = 4,THRESH_MASK = 7,THRESH_OTSU = 8,THRESH_TRIANGLE = 16)具体含义请查看opencv文档,默认不写是8(THRESH_OTSU)
- `thresh` - 整数型,阈值默认不写是150
- `maxthresh` - 整数型,最大阈值,默认不写255

**返回值**

- 返回查找的字符串

**说明**

- 请下载群里的光学ocr训练工具可以自己针对性的训练自己的字库

------

### ocrTextExJ

**获取指定范围内的文本字符串详细版**

```lua
ocrTextExJ(handle,x1,y1,x2,y2,[splitType],[ThresholdTypes],[thresh],[maxthresh])
```

**参数说明**

- `handle` - 整数型,tresseract-ocr对象句柄
- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `splitType` - 整数型,文本分割类型 0(RIL_BLOCK),1(RIL_PARA),2(RIL_TEXTLINE),3(RIL_WORD),4(RIL_SYMBOL) 默认不写是3(RIL_WORD)
- `ThresholdTypes` - 整数型,(THRESH_BINARY = 0,THRESH_BINARY_INV = 1,THRESH_TRUNC = 2,THRESH_TOZERO = 3,THRESH_TOZERO_INV = 4,THRESH_MASK = 7,THRESH_OTSU = 8,THRESH_TRIANGLE = 16)具体含义请查看opencv文档,默认不写是8(THRESH_OTSU)
- `thresh` - 整数型,阈值默认不写是150
- `maxthresh` - 整数型,最大阈值,默认不写255

**返回值**

- 返回查找的结果的表

**说明**

- 请下载群里的光学ocr训练工具可以自己针对性的训练自己的字库

------

### ocrj

**根据选中的字典查找该区域内所有文字和坐标**

```lua
ocrj(x1, y1, x2, y2,colorfmt,sim)
```

**参数说明**

- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `colorfmt` - 字符串,文字的颜色格式
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- json格式的字符串

------

### ocrjNew

**根据选中的字典查找该区域内所有文字和坐标(指定字库)**

```lua
ocrjNew(index,x1, y1, x2, y2,colorfmt,sim)
```

**参数说明**

- `index` - 字库的索引
- `x1` - 整数型,查找区域左上X坐标
- `y1` - 整数型,查找区域左上Y坐标
- `x2` - 整数型,查找区域右下X坐标
- `y2` - 整数型,查找区域右下Y坐标
- `colorfmt` - 字符串,文字的颜色格式
- `sim` - 双精度浮点数,相似度,取值范围0-1

**返回值**

- json格式的字符串

------

### releaseOcr

**释放指定句柄的tresseract-ocr对象**

```lua
releaseOcr(handle)
```

**参数说明**

- `handle` - 整数型,tresseract-ocr对象句柄

------

## 字库设置

### setDict

**设置字库文件**

```lua
setDict(index,name)
```

**参数说明**

- `index` - 整数型,需要设置的字典的索引目前取值范围0-29
- `name` - 字典的名称,目前字典只能放入资源文件中

**返回值**

- 0失败1成功

------

### useDict

**设置要查找的字典索引**

```lua
useDict(index)
```

**参数说明**

- `index` - 整数型,需要设置的字典的索引目前取值范围0-29

**返回值**

- 0失败1成功

**功能说明**

- 后面所有的查询都是在这个字典中查找

------

### setWhiteList

**设置本地光学OCR白名单**

```lua
setWhiteList(handle,whitelist)
```

**参数说明**

- `handle` - 通过createOcr创建的句柄
- `whiteList` - 字符串类型,不在这个字符集里面的字符全部被过滤掉

**说明**

- 如果你选择的光学ocr识别引擎使用的是lstm神经网络模式那么白名单将失效

------

## 屏幕设置

### setScreenScale

**屏幕缩放**

```lua
setScreenScale(type,width,height,[scale])
```

**参数说明**

- `type` - 0取消缩放,1开启缩放
- `width` - 整数型,脚本开发时使用的设备宽度
- `height` - 整数型,脚本开发时使用的设备高度
- `scale` - 可选参数,默认为1,0代表只对传入函数的坐标进行缩放,从函数返回坐标的不缩放,即为当前使用设备真实坐标。1代表传入函数的坐标进行缩放,函数返回的坐标进行反向缩放

**功能说明**

- 此功能用于非目标分辨率设备上找色和点击时使用

# 网络方法

## Java层HTTP请求

### LuaEngine.httpGet

**Java层HTTP GET请求**

```lua
LuaEngine.httpGet(url,params,timeout)
```

**参数说明**

- `url` - 字符串类型,访问的网络地址
- `params` - 数组类型,参数键值对
- `timeout` - 整数型,超时时间

------

### LuaEngine.httpPost

**Java层HTTP POST请求**

```lua
LuaEngine.httpPost(url,params,headers,timeout)
```

**参数说明**

- `url` - 字符串类型,访问的网络地址
- `params` - 数组类型,参数键值对
- `headers` - 数组类型,通params一样
- `timeout` - 整数型,超时时间

------

### LuaEngine.httpPostData

**Java层HTTP POST任意数据**

```lua
LuaEngine.httpPostData(url,params,headers,timeout)
```

**参数说明**

- `url` - 字符串类型,访问的网络地址
- `params` - 字符串类型
- `headers` - 字符串类型
- `timeout` - 整数型,超时时间

------

## 邮件发送

### LuaEngine.sendMail

**发送普通文本邮件**

```lua
LuaEngine.sendMail(账号,密码,发送给谁,邮箱服务器,是否开启认证,邮件标题,邮件内容,[发送结果回调])
```

**参数说明**

- `账号` - 字符串类型,需要用此账号的邮箱去发送邮件
- `密码` - 字符串类型
- `发送给谁` - 字符串类型
- `邮箱服务器` - 字符串类型
- `是否开启认证` - 布尔类型
- `邮件标题` - 字符串类型
- `邮件内容` - 字符串类型
- `发送结果回调` - 函数类型用于接收发送结果状态,可以不填写

------

### LuaEngine.sendMailWithFile

**发送带附件的邮件**

```lua
LuaEngine.sendMailWithFile(账号,密码,发送给谁,邮箱服务器,是否开启认证,邮件标题,邮件内容,文件路径,[发送结果回调])
```

**参数说明**

- `账号` - 字符串类型,需要用此账号的邮箱去发送邮件
- `密码` - 字符串类型
- `发送给谁` - 字符串类型
- `邮箱服务器` - 字符串类型
- `是否开启认证` - 布尔类型
- `邮件标题` - 字符串类型
- `邮件内容` - 字符串类型
- `文件路径` - 字符串类型
- `发送结果回调` - 函数类型用于接收发送结果状态,可以不填写

------

### LuaEngine.sendMailWithMultiFile

**发送带多个附件的邮件**

```lua
LuaEngine.sendMailWithMultiFile(账号,密码,发送给谁,邮箱服务器,是否开启认证,邮件标题,邮件内容,文件路径数组,[发送结果回调])
```

**参数说明**

- `账号` - 字符串类型,需要用此账号的邮箱去发送邮件
- `密码` - 字符串类型
- `发送给谁` - 字符串类型
- `邮箱服务器` - 字符串类型
- `是否开启认证` - 布尔类型
- `邮件标题` - 字符串类型
- `邮件内容` - 字符串类型
- `文件路径数组` - 数组表类型
- `发送结果回调` - 函数类型用于接收发送结果状态,可以不填写

------

## HTTP异步请求

### asynHttpGet

**HTTP GET异步请求**

```lua
asynHttpGet(callback,url,[timeout],[header])
```

**参数说明**

- `callback` - 函数类型,当服务器响应返回数据时调用该函数
- `url` - GET请求的网络地址可带参数
- `timeout` - 请求超时时间单位是秒可不填写默认30秒
- `header` - http请求头默认为空

**返回值**

- 无

------

### asynHttpPost

**HTTP POST异步请求**

```lua
asynHttpPost(callback,url,postdata,[timeout],[header])
```

**参数说明**

- `callback` - 函数类型,当服务器响应返回数据时调用该函数
- `url` - POST请求的网络地址可带参数
- `postdata` - 字符串类型,POST传递的数据
- `timeout` - 请求超时时间单位是秒可不填写默认30秒
- `header` - http请求头默认为空

**返回值**

- 无

------

## HTTP同步请求

### httpGet

**HTTP GET请求**

```lua
httpGet(url,[timeout],[header])
```

**参数说明**

- `url` - GET请求的网络地址可带参数
- `timeout` - 请求超时时间单位是秒可不填写默认30秒
- `header` - http请求头默认为空

**返回值**

- 字符串类型,http响应状态码

------

### httpPost

**HTTP POST请求**

```lua
httpPost(url,postdata,[timeout],[header])
```

**参数说明**

- `url` - POST请求的网络地址可带参数
- `postdata` - 字符串类型,POST传递的数据
- `timeout` - 请求超时时间单位是秒可不填写默认30秒
- `header` - http请求头默认为空

**返回值**

- 字符串类型,http响应状态码

------

## 文件操作

### downloadFile

**下载文件**

```lua
downloadFile(url,savepath,[progress])
```

**参数说明**

- `url` - 请求的网络地址
- `savepath` - 保存的文件路径
- `progress` - 可选项,函数类型,下载进度回调方法

**返回值**

- 失败返回-1,成功返回0

------

### uploadFile

**上传文件**

```lua
uploadFile(url,uploadfile,[timeout])
```

**参数说明**

- `url` - 请求的网络地址
- `uploadfile` - 要上传到服务器的文件路径

**返回值**

- 返回服务器的response字符串

------

## WebSocket连接

### startWebSocket

**打开一个WebSocket连接**

```lua
startWebSocket(url,onOpened,onClosed,onError,onRecv)
```

**参数说明**

- `url` - ws地址,目前只支持ws协议wss暂不支持
- `onOpend` - 当连接服务器成功后会回调这个函数
- `onClosed` - 当连接服务器断开后会回调这个函数
- `onError` - 当连接服务器失败后会回调这个函数
- `onRecv` - 当有接收到数据时会回调这个函数

**返回值**

- 返回一个数字类型,表示这个websocket链接的句柄

**使用事项**

- 以上所有回调函数是在单独的一个线程里面,请不要在回调函数中阻塞运行或者调用停止(exitScript)或者重启脚本(restartScript)函数,如果非要使用请在setTimer回调函数中执行,因为setTimer回调函数是运行在主线程中

------

### sendWebSocket

**向服务器发送数据**

```lua
sendWebSocket(handle,text)
```

**参数说明**

- `handle` - 数字类型,由startWebSocket返回的句柄
- `text` - 字符串类型,待发送的字符串

**返回值**

- 布尔类型,true成功,false失败

------

### closeWebSocket

**关闭一个WebSocket连接**

```lua
closeWebSocket(handle)
```

**参数说明**

- `handle` - 数字类型,由startWebSocket返回的句柄

**返回值**

- 布尔类型,true成功,false失败

**使用事项**

- 该函数只能在主线程中使用,所以需要在子线程中触发此函数可以调用setTimer在回调函数中调用关闭

------

# 触控方法

## 输入法控制

### imeLib.setText

**输入法模拟输入文字**

```lua
imeLib.setText(text)
```

**参数说明**

- `text` - 文本类型

**返回值**

- 返回布尔值,true表示成功false表示失败

------

### imeLib.deleteChar

**输入法删除一个字符**

```lua
imeLib.deleteChar()
```

**返回值**

- 返回布尔值,true表示成功false表示失败

------

### imeLib.finishInput

**输入法模拟完成输入**

```lua
imeLib.finishInput()
```

**返回值**

- 返回布尔值,true表示成功false表示失败

------

### imeLib.keyEvent

**输入法按键事件**

```lua
imeLib.keyEvent(action,keycode)
```

**参数说明**

- `action` - 0表示按下,1表示弹起
- `keycode` - 请自行参考android KeyEvent.KEY_CODE*

**返回值**

- 返回布尔值,true表示成功false表示失败

------

### imeLib.lock

**锁定使用懒人输入法**

```lua
imeLib.lock()
```

**返回值**

- 返回布尔值,true表示成功false表示失败

------

### imeLib.unlock

**解锁懒人输入法**

```lua
imeLib.unlock()
```

**返回值**

- 返回布尔值,true表示成功false表示失败

------

### inputText

**模拟输入文字**

```lua
inputText(text)
```

**参数说明**

- `text` - 文本类型

**返回值**

- 返回布尔值,true表示成功false表示失败

------

## 按键控制

### keyPress

**按一下按键并弹起**

```lua
keyPress(keycode)
```

**参数说明**

- `keycode` - 按键标识符或者数字按键码

**按键码对应表**

| 名称     | 标识符   | 按键码 |
| -------- | -------- | ------ |
| 主页键   | Home     | 3      |
| 返回键   | Back     | 4      |
| RECENT键 | Recent   | 187    |
| 打电话   | Call     | 5      |
| 挂电话   | EndCall  | 6      |
| 音量增加 | VolUp    | 24     |
| 音量减少 | VolDown  | 25     |
| 电源键   | Power    | 26     |
| 相机键   | Camera   | 27     |
| 菜单键   | Menu     | 82     |
| 向上翻页 | PageUp   | 92     |
| 向下翻页 | PageDown | 93     |

**返回值**

- 布尔类型

**注意事项**

- 此函数运行在root或者激活模式下所有按键全部有效在无障碍模式下只支持home back recent power 四个按键,其他均不支持

------

### keyDown

**按下一个按键不弹起**

```lua
keyDown(keycode)
```

**参数说明**

- `keycode` - 按键标识符或者数字按键码

**按键码对应表**

| 名称     | 标识符   | 按键码 |
| -------- | -------- | ------ |
| 主页键   | Home     | 3      |
| 返回键   | Back     | 4      |
| 打电话   | Call     | 5      |
| 挂电话   | EndCall  | 6      |
| 音量增加 | VolUp    | 24     |
| 音量减少 | VolDown  | 25     |
| 电源键   | Power    | 26     |
| 相机键   | Camera   | 27     |
| 菜单键   | Menu     | 82     |
| 向上翻页 | PageUp   | 92     |
| 向下翻页 | PageDown | 93     |

**返回值**

- 布尔类型

**注意事项**

- 此函数不支持在无障碍模式下使用,只能在激活或者root模式下使用,获取运行环境类型函数为getRunEnvType

------

### keyUp

**弹起一个按键**

```lua
keyUp(keycode)
```

**参数说明**

- `keycode` - 按键标识符或者数字按键码

**按键码对应表**

| 名称     | 标识符   | 按键码 |
| -------- | -------- | ------ |
| 主页键   | Home     | 3      |
| 返回键   | Back     | 4      |
| 打电话   | Call     | 5      |
| 挂电话   | EndCall  | 6      |
| 音量增加 | VolUp    | 24     |
| 音量减少 | VolDown  | 25     |
| 电源键   | Power    | 26     |
| 相机键   | Camera   | 27     |
| 菜单键   | Menu     | 82     |
| 向上翻页 | PageUp   | 92     |
| 向下翻页 | PageDown | 93     |

**返回值**

- 布尔类型

**注意事项**

- 此函数不支持在无障碍模式下使用,只能在激活或者root模式下使用,获取运行环境类型函数为getRunEnvType

------

## 触摸控制

### tap

**点击坐标**

```lua
tap(x,y)
```

**参数说明**

- `x` - 整数型,当前屏幕横坐标
- `y` - 整数型,当前屏幕纵坐标

**返回值**

- 无

------

### longTap

**长点击坐标**

```lua
longTap(x,y)
```

**参数说明**

- `x` - 整数型,当前屏幕横坐标
- `y` - 整数型,当前屏幕纵坐标

**返回值**

- 无

------

### swipe

**模拟从一点滑动到另外一点**

```lua
swipe(x1,y1,x2,y2,time)
```

**参数说明**

- `x1` - 整数型,划动的起点x坐标
- `y1` - 整数型,划动的起点y坐标
- `x2` - 整数型,划动的终点x坐标
- `y2` - 整数型,划动的终点y坐标
- `time` - 整数型,滑动到x,y坐标点所需要的时间单位是毫秒

**返回值**

- 布尔类型

**注意事项**

- 此函数在懒人高级版所有模式下都能使用

------

## 多点触控

### touchDown

**按住不放**

```lua
touchDown(id,x,y)
```

**参数说明**

- `id` - 模拟手指的索引号0-4之间
- `x` - 整数型,当前屏幕横坐标
- `y` - 整数型,当前屏幕纵坐标

**返回值**

- 无

**注意事项**

- 此函数不支持在无障碍模式下使用,只能在激活或者root模式下使用

------

### touchMove

**模拟滑动**

```lua
touchMove(id,x,y)
```

**参数说明**

- `id` - 模拟手指的索引号0-4之间
- `x` - 整数型,当前屏幕横坐标
- `y` - 整数型,当前屏幕纵坐标

**返回值**

- 布尔类型

**注意事项**

- 此函数不支持在无障碍模式下使用,只能在激活或者root模式下使用

------

### touchMoveEx

**模拟滑动增强版**

```lua
touchMoveEx(id,x,y,time)
```

**参数说明**

- `id` - 模拟手指的索引号0-4之间
- `x` - 整数型,当前屏幕横坐标
- `y` - 整数型,当前屏幕纵坐标
- `time` - 整数型,滑动到x,y坐标点所需要的时间单位是毫秒

**返回值**

- 无

------

### touchUp

**弹起手指**

```lua
touchUp(id)
```

**参数说明**

- `id` - 模拟手指的索引号0-4之间

**返回值**

- 布尔类型

**注意事项**

- 此函数不支持在无障碍模式下使用,只能在激活或者root模式下使用

------

## 触摸监听

### setOnTouchListener

**获取用户触摸屏幕坐标**

```lua
setOnTouchListener(onTouchEvent)
```

**参数说明**

- `onTouchEvent` - 回调函数,当用户触摸屏幕时会触发调用这个函数并传递坐标

**返回值**

- 返回一个布尔类型,true表示成功,false表示失败

**注意事项**

- 此函数不支持在无障碍模式下使用,只能在激活或者root模式下使用(android虚拟机是无效的),获取运行环境类型函数为getRunEnvType

------

# 节点操作方法

## 节点基础操作

### 向左滚动
```lua
语法: node:scrollLeft()
返回值: 返回布尔类型，true表示成功反之失败
```

### 向右滚动
```lua
语法: node:scrollRight()
返回值: 返回布尔类型，true表示成功反之失败
```

### 滚动滚动条控件到指定位置
```lua
语法: node:scrollTo(row,col)
参数: row如果是垂直滚动条则代表滚动到第几行，如果是水平滚动条则用col滚动到第几列
返回值: 返回布尔类型，true表示成功反之失败
```

### 向上滚动
```lua
语法: node:scrollUp()
返回值: 返回布尔类型，true表示成功反之失败
```

### 选择选项、标记复选框、选择单选按钮、选择列表项等操作
```lua
语法: node:select
返回值: 返回布尔类型，true表示成功反之失败
```

### 设置节点所在控件的进度条的进度
```lua
语法: node:setProgress(1.0)
参数: pos 浮点类型
返回值: 返回布尔类型，true表示成功反之失败
```

### 选择选项、标记复选框、选择单选按钮、选择列表项等操作
```lua
语法: node:setSelection(start,end)
参数: start,end 选中从第start到end的字符串
返回值: 返回布尔类型，true表示成功反之失败
```

### 设置节点文本
```lua
语法: node:setText(str)
参数: str要输入的字符串
返回值: 返回布尔类型，true表示成功反之失败
```

### 获取节点的text属性值
```lua
语法: node:text()
返回值: 返回text
```

### 获取节点的所有属性以json字符串的形式
```lua
语法: node:toJson()
返回值: 返回json字符串
```

## 节点库操作

### 自动关闭无障碍服务
```lua
语法: nodeLib.closeAccessibility()
返回值: 布尔类型
注意: 只有root或者激活模式有效
```

### 获取当前界面节点xml数据
```lua
语法: nodeLib.getNodeXml()
返回值: 返回xml字符串
```

### 锁定节点到内存
```lua
语法: nodeLib.lockNode()
注意: 这个方法是配合新版节点函数使用注意和keepNode(旧版的节点函数)区别开了
返回值: 布尔类型
```

### 自动打开无障碍服务
```lua
语法: nodeLib.openAccessibility()
返回值: 布尔类型
注意: root或者激活模式下会自动开启，其它模式下需要手动开启
```

### 导出当前界面节点数据
```lua
语法: nodeLib.saveNode(path)
返回值: 布尔类型
```

### 解锁节点
```lua
语法: nodeLib.unlockNode()
注意: 这个方法是配合新版节点函数使用注意和releaseNode(旧版的节点函数)区别开了
返回值: 布尔类型
```

## 节点选择器

### 根据packageName属性全字段匹配
```lua
语法: packageName(str)
参数说明: str 字符串类型，指定要匹配节点所属的包名
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 匹配包含指定字符串的的节点
```lua
语法: packageNameContains(str)
参数说明: str 字符串类型，包含该字符串的包名的节点将被匹配到
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 匹配包名后缀为指定字符串的的节点
```lua
语法: packageNameEndsWith(str)
参数说明: str 字符串类型，包名为该字符的后缀的节点将被匹配到
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 包名正则匹配
```lua
语法: packageNameMatches(str)
参数说明: str 字符串类型，根据这个正则字符串匹配所有符合规则包名的节点
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 匹配包名前缀为指定字符串的的节点
```lua
语法: packageNameStartsWith(str)
参数说明: str 字符串类型，包名为该字符的前缀的节点将被匹配到
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据是否是密码框匹配
```lua
语法: password(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据是否可以滚动来匹配
```lua
语法: scrollable(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

## 选择器操作

### 点击所有匹配到的节点
```lua
语法: sel:click()
说明: 此方法会根据选择器里面的匹配属性去匹配直到匹配到节点然后去点击，如果匹配结果为空那么将会一直等待
返回值: 返回布尔类型，true表示成功反之失败
```

### 获取所有匹配的节点
```lua
语法: findAll([timeout])
参数说明: timeout 匹配超时时间单位是毫秒，如果不填写将一直等待直到匹配到结果才会返回
返回值: 获取匹配到的所有节点对象
```

### 根据索引获取匹配到的节点
```lua
语法: findOnce([index])
参数说明: index 整数型，表示要获取所有符合条件结果中的第几个，当index不写表示0也就是第一个节点
返回值: 返回匹配到的一个节点对象
```

### 获取第一个节点
```lua
语法: findOne([timeout])
参数说明: timeout 匹配超时时间单位是毫秒，如果不填写将一直等待直到匹配到结果才会返回
返回值: 返回匹配到的一个节点对象
```

### 长按点击所有匹配到的节点
```lua
语法: sel:longClick()
说明: 此方法会根据选择器里面的匹配属性去匹配直到匹配到节点然后去长按点击，如果匹配结果为空那么将会一直等待
返回值: 返回布尔类型，true表示成功反之失败
```

### 根据是否可见匹配
```lua
语法: visibleToUser(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据节点的text属性全匹配
```lua
语法: text(str)
参数说明: str 字符串类型，指定要查找的具体控件的text
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据text包含的部分字符串匹配
```lua
语法: textContains(str)
参数说明: str 字符串类型，指定要查找的模糊的包含text某一段字符串的内容
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据text的后缀去匹配
```lua
语法: textEndsWith(str)
参数说明: str 字符串类型，指定要查找的text的后缀
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 正则匹配text
```lua
语法: textMatches(reg)
参数: reg 是一个正则字符串表达式，具体正则请参考java
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据text的前缀去匹配
```lua
语法: textStartsWith(str)
参数说明: str 字符串类型，指定要查找的text的前缀
返回值: 返回一个选择器对象,该对象支持级联选择
```

## 节点属性选择器

### 根据节点的范围匹配
```lua
语法: bounds(l,t,r,b)
参数说明: l 左范围,t 上范围,r右范围,b下范围
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 匹配该范围内的节点
```lua
语法: boundsInside(l,t,r,b)
参数说明: l 左范围,t 上范围,r右范围,b下范围
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 是否可以被勾选来匹配
```lua
语法: checkable(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据是否被勾选来匹配
```lua
语法: checked(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据节点的className属性全匹配
```lua
语法: className(str)
参数说明: str 字符串类型，指定要查找的具体控件的类名
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据className属性所包含的字符串模糊匹配
```lua
语法: classNameContains(str)
参数说明: str 字符串类型，指定要查找className包含该字符的节点
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据className属性后缀匹配
```lua
语法: classNameEndsWith(str)
参数说明: str 字符串类型，指定要查找className后缀为该字符的节点
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据className属性正则匹配
```lua
语法: classNameMatches(str)
参数说明: str 字符串类型，指定要匹配className的正则表达式
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据className属性前缀匹配
```lua
语法: classNameStartsWith(str)
参数说明: str 字符串类型，指定要查找className前缀为该字符的节点
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据是否可点击匹配
```lua
语法: clickable(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据深度索引匹配
```lua
语法: depth(level)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据节点的desc属性全匹配
```lua
语法: desc(str)
参数说明: str 字符串类型，指定要查找的具体控件的desc
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据desc包含的部分字符串匹配
```lua
语法: descContains(str)
参数说明: str 字符串类型，指定要查找的模糊的包含desc某一段字符串的内容
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据desc的后缀去匹配
```lua
语法: descEndsWith(str)
参数说明: str 字符串类型，指定要查找的desc的后缀
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 正则匹配desc
```lua
语法: descMatches(reg)
参数: reg 是一个正则字符串表达式，具体正则请参考java
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据desc的前缀去匹配
```lua
语法: descStartsWith(str)
参数说明: str 字符串类型，指定要查找的desc的前缀
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 匹配该范围内的节点
```lua
语法: drawingOrder(level)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据是否可用匹配
```lua
语法: enabled(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 是否可以被勾选来匹配
```lua
语法: focusable(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据是否抢占了焦点来匹配
```lua
语法: focused(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据节点的id属性全匹配
```lua
语法: id(str)
参数说明: str 字符串类型，指定要查找的具体控件的id
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据id包含的部分字符串匹配
```lua
语法: idContains(str)
参数说明: str 字符串类型，指定要查找的模糊的包含id某一段字符串的内容
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据id的后缀去匹配
```lua
语法: idEndsWith(str)
参数说明: str 字符串类型，指定要查找的id的后缀
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 正则匹配id
```lua
语法: idMatches(reg)
参数: reg 是一个正则字符串表达式，具体正则请参考java
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据id的前缀去匹配
```lua
语法: idStartsWith(str)
参数说明: str 字符串类型，指定要查找的id的前缀
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据在当前父节点里面的索引顺序查找
```lua
语法: index(level)
返回值: 返回一个选择器对象,该对象支持级联选择
```

### 根据是否可长按点击匹配
```lua
语法: longClickable(b)
返回值: 返回一个选择器对象,该对象支持级联选择
```

## 节点详细操作

### 获取当前节点的范围信息
```lua
语法: node:bounds()
返回值: 返回4个整数，依次是左范围，上范围，右范围，下范围
```

### 获取当前节点相对父节点中的范围信息
```lua
语法: node:boundsInParent()
返回值: 返回4个整数，依次是左范围，上范围，右范围，下范围
```

### 获取当前节点的子节点个数
```lua
语法: node:childCount()
```

### 获取当前节点下的所有子节点
```lua
语法: node:childs()
```

### 点击节点
```lua
语法: node:click()
返回值: 返回布尔类型，true表示成功反之失败
```

### 折叠控件
```lua
语法: node:collapse()
返回值: 返回布尔类型，true表示成功反之失败
```

### 右键点击节点
```lua
语法: node:contextClick()
返回值: 返回布尔类型，true表示成功反之失败
```

### 获取输入框的文本到剪贴板
```lua
语法: node:copy()
返回值: 返回布尔类型，true表示成功反之失败
```

### 剪切输入框选中的内容
```lua
语法: node:cut()
返回值: 返回布尔类型，true表示成功反之失败
```

### 获取当前节点深度索引
```lua
语法: node:depth()
返回值: 返回当前节点的绘制顺序的索引
```

### 获取节点的desc属性值
```lua
语法: node:desc()
返回值: 返回desc
```

### 获取当前节点的绘制顺序的索引
```lua
语法: node:drawingOrder()
返回值: 返回当前节点的绘制顺序的索引
```

### 展开控件
```lua
语法: node:expand()
返回值: 返回布尔类型，true表示成功反之失败
```

### 抢占焦点
```lua
语法: node:focus()
返回值: 返回布尔类型，true表示成功反之失败
```

### 获取节点的id属性值
```lua
语法: node:id()
返回值: 返回id
```

### 长按当前节点
```lua
语法: node:longClick()
返回值: 返回布尔类型，true表示成功反之失败
```

### 获取节点的packageName属性值
```lua
语法: node:packageName()
返回值: 返回packageName
```

### 获取父节点
```lua
语法: node:parent()
```

### 粘贴剪贴板里面的内容到节点的输入框
```lua
语法: node:paste()
返回值: 返回布尔类型，true表示成功反之失败
```

### 向后滚动
```lua
语法: node:scrollBackward()
返回值: 返回布尔类型，true表示成功反之失败
```

### 向下滚动
```lua
语法: node:scrollDown()
返回值: 返回布尔类型，true表示成功反之失败
```

### 向前滚动
```lua
语法: node:scrollForward()
返回值: 返回布尔类型，true表示成功反之失败
```

# 动态UI方法

## UI控件创建

### 创建一个按钮
```lua
语法: ui.addButton(layout,name,text,[w],[h])
参数说明:
  layout: 布局名称
  name: 按钮名称
  text: 按钮显示内容
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 创建多选框控件
```lua
语法: ui.addCheckBox(layout,name,text,[sel],[w],[h])
参数说明:
  layout: 布局名称
  name: 控件名称
  text: 显示内容
  sel: 布尔值类型，true表示选中,false不选中
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 创建输入框控件
```lua
语法: ui.addEditText(layout,name,text,[w],[h])
参数说明:
  layout: 布局名称
  name: 控件名称
  text: 显示内容
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 创建图像控件
```lua
语法: ui.addImageView(layout,name,path,[w],[h])
参数说明:
  layout: 布局名称
  name: 控件名称
  path: 图片路径
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 创建线控件
```lua
语法: ui.addLine(layout,name)
参数说明:
  layout: 布局名称
  name: 控件名称
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 创建单选框组控件
```lua
语法: ui.addRadioGroup(layout,name,list,[select],[w],[h],[horiziontal])
参数说明:
  layout: 布局名称
  name: 控件名称
  list: 表格类型,显示内容
  select: 整数类型,默认选中第几个单选框(默认0),可省略
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
  horiziontal: 布尔类型是否横向布局
返回值: 布尔类型
```

### 创建下拉框控件
```lua
语法: ui.addSpinner(layout,name,list,select,[w],[h])
参数说明:
  layout: 布局名称
  name: 控件名称
  list: 表格类型,显示内容
  select: 整数类型,默认选中第几个单选框(默认0),可省略
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 创建子标签页控件
```lua
语法: ui.addTab(tabName,name,text)
参数说明:
  tabName: 父标签控件名称
  name: 子标签控件名称
  text: 标签显示内容
返回值: 布尔类型
```

### 创建标签页控件
```lua
语法: ui.addTabView(layout,name,[h])
参数说明:
  layout: 布局名称
  name: 控件名称
  h: 高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 创建文字框控件
```lua
语法: ui.addTextView(layout,name,text,[w],[h])
参数说明:
  layout: 布局名称
  name: 控件名称
  text: 显示内容
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 创建一个浏览器控件
```lua
语法: ui.addWebView(layout,name,url,[w],[h])
参数说明:
  layout: 布局名称
  name: 控件名称
  url: 访问的地址
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

## UI布局管理

### 创建一个快速ui命令队列
```lua
语法: ui.beginUiQueue()
说明: 这个函数目的是为了将后面使用的所有ui命令全部放入一个新创建的队列而不是直接执行
直到执行了ui.endUiQueue命令后一次性将ui命令全部执行避免了频繁的ui命令调用降低系统的io消耗让执行效率提升
特别是复杂的动态ui界面，更加节约时间
返回值: 布尔类型
```

### 执行这个ui命令队列
```lua
语法: ui.endUiQueue()
返回值: 布尔类型
```

### 创建一个新的布局
```lua
语法: ui.newLayout(name,[w],[h])
参数说明:
  name: 字符串型,要新建的布局名称，即标题
  w: 宽(-1表示填满.-2是自适应)(默认值: 自适应大小),可省略
  h: 高(-1表示填满.-2是自适应)(默认值: 自适应大小),可省略
返回值: 布尔类型
```

### 布局换行排列
```lua
语法: ui.newRow(layout,rowid,[w],[h])
参数说明:
  layout: 布局名称
  rowid: 换行布局名称
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 显示一个布局
```lua
语法: ui.show(name,[isShowBottom],[x],[y],[isShowTitle])
参数说明:
  name: 字符串类型, 要显示的布局名称
  isShowBottom: 布尔类型, 是否显示底部默认继续和退出按钮控制栏，默认不写是true
  isShowTitle: 布尔类型, 是否显示标题栏，默认不写是true
  x,y: 初始化位置，默认不写是居中
返回值: 布尔类型
```

### 关闭一个布局
```lua
语法: ui.dismiss(name)
参数说明:
  name: 字符串类型, 要显示的布局名称
返回值: 布尔类型
```

## UI控件操作

### 调用webview打开的网页中的js函数
```lua
语法: ui.callJs(name,js)
参数说明:
  name: 控件名称
  js: 字符串类型，js回调函数字符串
返回值: 布尔类型
```

### 获取当前界面所有控件的值
```lua
语法: ui.getData()
返回值: 表格类型
```

### 获取可用状态
```lua
语法: ui.getEnabled(name)
参数说明:
  name: 控件名称
返回值: 布尔类型
```

### 获取文字
```lua
语法: ui.getText(name)
参数说明:
  name: 控件名称
返回值: 字符串类型
```

### 获取文字颜色
```lua
语法: ui.getTextColor(name)
参数说明:
  name: 控件名称
返回值: 字符串类型
```

### 获取控件值
```lua
语法: ui.getValue(name)
参数说明:
  name: 控件名称
返回值: 字符串类型
```

### 获取显示状态
```lua
语法: ui.getVisible(name)
参数说明:
  name: 控件名称
返回值: 布尔类型
```

### 读取设置
```lua
语法: ui.loadProfile(path)
参数说明:
  path: 配置文件路径
返回值: 布尔类型
```

### 保存配置
```lua
语法: ui.saveProfile(path)
参数说明:
  path: 配置文件路径
返回值: 布尔类型
```

## UI控件设置

### 设置控件背景颜色
```lua
语法: ui.setBackground(name,color)
参数说明:
  name: 控件名称
  color: 字符串类型,颜色格式例子:"#ffff00ff"
返回值: 布尔类型
```

### 重设按钮控件
```lua
语法: ui.setButton(name,text,[w],[h])
参数说明:
  name: 按钮控件名称
  text: 按钮显示文字内容
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 重设多选框控件
```lua
语法: ui.setCheckBox(name,text,sel,[w],[h])
参数说明:
  name: 按钮控件名称
  text: 文本框显示文字内容
  sel: true表示选中，false不选中
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 设置输入框默认提示字符串
```lua
语法: ui.setEditHintText(name,hinttext)
参数说明:
  name: 按钮控件名称
  hinttext: 输入框提示文字内容
返回值: 布尔类型
```

### 重设输入框控件
```lua
语法: ui.setEditText(name,text,[w],[h])
参数说明:
  name: 按钮控件名称
  text: 输入框显示文字内容
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 设置控件可用状态
```lua
语法: ui.setEnable(name,enable)
参数说明:
  name: 控件名称
  enable: 布尔类型，true可用，false不可用
返回值: 布尔类型
```

### 设置控件宽度全屏
```lua
语法: ui.setFullScreen(name)
参数说明:
  name: 控件名称
返回值: 字符串类型
```

### 设置控件对齐方式
```lua
语法: ui.setGravity(name,alignMode)
参数说明:
  name: 控件名称
  alignMode: 整数类型,48 上对齐,80 下对齐,3 左对齐,5 右对齐,16 垂直居中对齐,17 居中对齐
返回值: 布尔类型
```

### 重设图像控件
```lua
语法: ui.setImageView(name,path,[w],[h])
参数说明:
  name: 控件名称
  path: 图片路径
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 设置输入类型
```lua
语法: ui.setInputType(name,type)
参数说明:
  name: 控件名称
  type: 整数类型,
    1:输入类型为普通文本
    2:输入类型为数字文本
    3:输入类型为电话号码
    4:输入类型为日期和时间
    128:输入一个密码
返回值: 布尔类型
```

### 重设线控件
```lua
语法: ui.setLine(name,[w],[h])
参数说明:
  name: 线控件名称
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 设置控件改变事件
```lua
语法: ui.setOnChange(name,event)
参数说明:
  name: 控件名称
  event: 字符串类型，可以是一个lua代码字符串
返回值: 布尔类型
```

### 设置控件单击事件
```lua
语法: ui.setOnClick(name,event)
参数说明:
  name: 控件名称
  event: 字符串类型，可以是一个lua代码字符串
返回值: 布尔类型
```

### 设置控件关闭事件
```lua
语法: ui.setOnClose(event)
参数说明:
  event: 字符串类型，可以是一个lua代码字符串
返回值: 布尔类型
```

### 设置控件内边距
```lua
语法: ui.setPadding(name,l,t,r,b)
参数说明:
  name: 控件名称
  l,t,r,b: 整数类型表示内边距的左 上 右 下
返回值: 字符串类型
```

### 重设单选框控件
```lua
语法: ui.setRadioGroup(name,list,select,[w],[h],[horiziontal])
参数说明:
  name: 按钮控件名称
  list: 表格类型,显示内容
  select: 整数类型，表示第几个选中
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
  horiziontal: 布尔类型是否横向布局
返回值: 布尔类型
```

### 批量设置行控件显示状态通过gid
```lua
语法: ui.setRowVisibleByGid(layoutid,name,state)
参数说明:
  layoutid: 布局id
  name: 控件名称
  state: 整数类型,2隐藏空间，3隐藏切让控件不占用布局空间
返回值: 布尔类型
```

### 重设下拉框控件
```lua
语法: ui.setSpinner(name,list,select,[w],[h])
参数说明:
  name: 按钮控件名称
  list: 表格类型,显示内容
  select: 整数类型，表示第几个选中
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 控件设置文字
```lua
语法: ui.setText(name,text)
参数说明:
  name: 控件名称
  text: 文字内容
返回值: 布尔类型
```

### 设置文字颜色
```lua
语法: ui.setTextColor(name,color)
参数说明:
  name: 控件名称
  color: 字符串类型,颜色格式例子:"#ffff00ff"
返回值: 布尔类型
```

### 设置文字大小
```lua
语法: ui.setTextSize(layout,size)
参数说明:
  layout: 布局名称
  size: 整数类型文字大小
返回值: 布尔类型
```

### 重设文本框控件
```lua
语法: ui.setTextView(name,text,[w],[h])
参数说明:
  name: 按钮控件名称
  text: 文本框显示文字内容
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

### 设置标题背景颜色
```lua
语法: ui.setTitleBackground(layout,color)
参数说明:
  layout: 布局名称
  color: 字符串类型,颜色格式例子:"#ffff00ff"
返回值: 布尔类型
```

### 设置布局标题
```lua
语法: ui.setTitleText(layout,text)
参数说明:
  layout: 布局名称
  text: 文字内容
返回值: 布尔类型
```

### 设置控件显示状态
```lua
语法: ui.setVisiblity(name,state)
参数说明:
  name: 控件名称
  state: 整数类型,2隐藏空间，3隐藏切让控件不占用布局空间
返回值: 布尔类型
```

### 重设浏览器控件
```lua
语法: ui.setWebView(name,url,[w],[h])
参数说明:
  name: 控件名称
  url: 访问的地址
  w,h: 宽高(-1表示填满.-2是自适应)默认可以不填写表示自适应
返回值: 布尔类型
```

# JSON方法

### 把json字符串转换成lua表格
```lua
语法: jsonLib.decode(json)
参数说明:
  json: 字符串类型
返回值: lua表格
```

### 把lua表格编码成json字符串
```lua
语法: jsonLib.encode(table)
参数说明:
  table: lua表格
返回值: json字符串
```

# 控制台方法

### 清除控制台中的日志
```lua
语法: console.clearLog()
返回值: 无
```

### 关闭控制台窗口
```lua
语法: console.dismiss()
返回值: 布尔类型
```

### 锁定控制台窗口
```lua
语法: console.lockConsole()
说明: 锁定后窗口将无法触摸拖动，事件直接透传到下面的窗口中
```

### 打印日志到控制台窗口
```lua
语法: console.println(level,log)
参数说明:
  level: 整数类型表示日志类别 1.VERBOSE 2.DEBUG 3.INFO 4.WARN 5.ERROR 6.ASSERT
  log: 要打印的日志
返回值: 无
```

### 设置控制台位置和大小
```lua
语法: console.setPos(x,y,[w],[h])
参数说明:
  x,y: 是窗口的坐标位置
  w,h: 选填表示窗口大小
返回值: 无
```

### 设置控制台标题
```lua
语法: console.setTitle(title)
参数说明:
  title: 字符串类型
返回值: 无
```

### 创建并显示控制台窗口
```lua
语法: console.show([isShow])
参数说明:
  isShow: 布尔类型，true表示创建并显示控制台窗口，false表示创建但不显示窗口，默认不填写是true
返回值: 布尔类型
```

### 显示或者隐藏控制台标题栏
```lua
语法: console.showTitle([isShow])
参数说明:
  isShow: 布尔类型,true显示，false不显示
返回值: 布尔类型
```

### 解除锁定控制台窗口
```lua
语法: console.unlockConsole()
```

# IMGUI方法

## IMGUI基础操作

### 检测IMGUI支持状态
```lua
语法: imgui.isSupport()
参数说明: 无参数
返回值: boolean类型
  - true: 当前环境支持IMGUI
  - false: 当前环境不支持IMGUI
注意事项:
1. 应在调用其他imgui函数前先检测支持情况
2. 返回false时调用其他imgui函数可能产生未定义行为
3. 典型不支持情况包括：
   - 在不兼容的Android版本上运行
   - 缺少必要的OpenGL ES3支持
   - 系统内存不足
```

### 关闭imgui框架
```lua
语法: imgui.close()
参数说明: 无参数
返回值: 无
重要说明:
1. 当使用imgui.show(true)显示框架时(启用触摸交互)，show函数会阻塞直到框架被关闭
2. 此函数用于强制关闭当前显示的imgui框架，使阻塞的show()调用返回
3. 通常在定时器或外部事件中调用此函数来主动关闭框架
4. 关闭后所有窗口资源仍有效，需要手动调用destroyWindow销毁窗口
5. 与setOnClose回调配合使用时，应先触发关闭回调再调用此函数
```

### 设置imgui颜色主题
```lua
语法: imgui.setColorTheme(style)
参数说明: style 1表示浅色主题 2表示经典主题 其它值表示黑色主题
返回值: boolean类型
  - true: 当前环境支持IMGUI
  - false: 当前环境不支持IMGUI
```

## IMGUI窗口管理

### 创建一个imgui窗口
```lua
语法: imgui.createWindow(title,x,y,width,height,showclose)
参数说明:
  title: 窗口标题
  x,y: 窗口左上角相对屏幕的位置
  width,height: 窗口的大小
  showclose: 是否显示关闭按钮
返回值: 窗口的句柄
```

### 销毁一个imgui窗口
```lua
语法: imgui.destroyWindow(handle)
参数说明: handle 要销毁的窗口句柄（由createWindow创建返回的值）
返回值: 无
注意事项:
1. 销毁窗口后，对应的句柄将失效，不应再使用
2. 建议在窗口关闭回调中使用此函数确保资源释放
3. 重复销毁同一窗口可能导致错误
```

### 获取窗口位置和尺寸
```lua
语法: imgui.getWindowPos(windowHandle)
参数说明:
  windowHandle: 目标窗口的句柄(整数)
返回值:
- 成功时返回包含窗口位置和尺寸的表，结构为:
  {
    x = 窗口左上角X坐标(浮点数),
    y = 窗口左上角Y坐标(浮点数),
    width = 窗口宽度(浮点数),
    height = 窗口高度(浮点数)
  }
- 失败时返回nil
注意事项:
1. 坐标系统以屏幕左上角为原点(0,0)
2. 所有尺寸单位为像素
3. 需要确保传入的窗口句柄有效
4. 如果窗口被最小化或隐藏，可能无法获取正确位置
典型应用场景:
- 窗口位置记忆和恢复
- 多窗口布局计算
- 窗口拖拽限制边界检查
```

### 设置窗口关闭回调
```lua
语法: imgui.setOnClose(window_handle, callback_function)
参数说明:
  window_handle: 窗口句柄(整数)
  callback_function: 回调函数(必须接受1个参数)
回调函数参数:
  h: 触发关闭事件的窗口句柄(整数)
注意事项:
1. 必须在窗口显示前设置回调
2. 回调中应调用destroyWindow释放资源
```

## IMGUI布局容器

### 创建垂直布局容器
```lua
语法: imgui.createVerticalLayout(parent, width, height)
参数说明:
  parent: 父容器/窗口的句柄(整数)
  width (可选): 布局宽度(浮点数)，默认为0
    - 值为-1时表示填满父容器宽度
  height (可选): 布局高度(浮点数)，默认为0
    - 值为-1时表示填满父容器高度
返回值: 新创建的垂直布局句柄(整数)，失败返回0
注意事项:
1. 子控件将按照添加顺序垂直排列
2. 当width/height为0时使用自动计算大小
3. 当width/height为-1时填充满父容器
4. 必须先创建父容器再创建子布局
```

### 创建水平布局容器
```lua
语法: imgui.createHorticalLayout(parent, width, height)
参数说明:
  parent: 父容器/窗口的句柄(整数)
  width (可选): 布局宽度(浮点数)，默认为0
    - 值为-1时表示填满父容器宽度
  height (可选): 布局高度(浮点数)，默认为0
    - 值为-1时表示填满父容器高度
返回值: 新创建的水平布局句柄(整数)，失败返回0
注意事项:
1. 子控件将按照添加顺序水平排列
2. 当width/height为0时使用自动计算大小
3. 当width/height为-1时填充满父容器
4. 必须先创建父容器再创建子布局
5. 注意函数名是HorticalLayout(不是Horizontal)
```

### 创建树形布局容器
```lua
语法: imgui.createTreeBoxLayout(parent, title, width)
参数说明:
  parent: 父容器句柄(整数)
  title: 树形框标题文本(字符串)
  width: 树形框宽度(浮点数，像素单位)
返回值:
- 成功返回树形框句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
```

### 设置容器布局控件是否显示边框
```lua
语法: imgui.setLayoutBorderVisible(handle, visible)
参数说明:
  handle: 控件句柄(整数)
  visible: 可见性状态，支持多种格式:
    - boolean: true表示显示，false表示隐藏
返回值: 无返回值
```

### 设置控件同行显示
```lua
语法: imgui.sameLine(handle, spacing)
参数说明:
  handle: 控件句柄(整数)
  spacing: 与前一个控件的间距(浮点数，像素单位)
返回值:
- 成功返回true
- 失败返回false，可通过imgui.getLastError()获取错误信息
注意事项:
1. 使当前控件与前一个控件在同一行显示
2. spacing为-1 表示系统计算
```

## IMGUI控件创建

### 创建按钮控件
```lua
语法: imgui.createButton(parent, label, width, height)
参数说明:
  parent: 父容器/窗口的句柄(整数)
  label: 按钮上显示的文本(字符串)
  width (可选): 按钮宽度(浮点数)，默认为0
    - 值为0时自动计算合适宽度
    - 值为-1时填满父容器宽度
  height (可选): 按钮高度(浮点数)，默认为0
    - 值为0时自动计算合适高度
    - 值为-1时填满父容器高度
返回值: 新创建的按钮句柄(整数)，失败返回0
注意事项:
1. 按钮会继承父容器的布局方向(垂直/水平)
2. 当width/height为0时根据文本内容自动计算大小
3. 当width/height为-1时填充满父容器可用空间
4. 需要先创建有效的父容器
5. 按钮点击事件需通过setOnClick回调设置
```

### 创建复选框控件
```lua
语法: imgui.createCheckBox(parent, label, checked)
参数说明:
  parent: 父容器句柄(由createWindow/createVerticalLayout等创建)
  label: 复选框右侧显示的文本标签(字符串)
  checked (可选): 初始选中状态(布尔值)，默认为false
返回值:
- 成功: 复选框控件句柄(>0的整数)
- 失败: 0 (可能原因：IMGUI不支持/无效父句柄/内存不足)
注意事项:
1. 必须先调用imgui.isSupport()检查环境支持性
2. 复选框会继承父容器的布局方向
3. 标签文本支持UTF-8编码
4. 失败时应查询imgui.getLastError()获取详情
```

### 创建滑动开关控件
```lua
语法: imgui.createSwitch(parent, label, checked, height)
参数说明:
  parent: 父容器句柄(由createWindow等创建)
  label: 开关右侧显示的文本(字符串)
  checked: 初始状态(布尔值)，默认为false
  height: 开关高度(浮点数)，不能为0
返回值:
- >0: 开关控件句柄
- 0: 创建失败(可通过imgui.getLastError()获取原因)
注意事项:
1. 必须先调用imgui.isSupport()检查支持性
2. 高度为0时根据系统DPI自动计算合适尺寸
3. 文本支持多语言UTF-8编码
4. 开关颜色可通过imgui.setStyleColor自定义
```

### 创建文本标签控件
```lua
语法: imgui.createLabel(parent, text, singleline)
参数说明:
  parent: 父容器句柄(由createWindow/createVerticalLayout等创建)
  text: 要显示的文本内容(字符串)
  singleline (可选): 是否单行显示(布尔值)，默认为true
    - true: 单行显示(超出部分截断)
    - false: 多行显示(自动换行)
返回值:
- >0: 标签控件句柄
- 0: 创建失败(使用imgui.getLastError()获取详情)
注意事项:
1. 必须先调用imgui.isSupport()确认环境支持
2. 文本内容支持UTF-8编码和转义字符(\n,\t等)
3. 多行模式下高度自动计算
4. 可通过imgui.setWidgetText更新内容
```

### 创建文本输入框
```lua
语法: imgui.createInputText(parent, label, value, inputType, width, height)
参数说明:
  parent: 父容器句柄
  label: 输入框左侧标签(支持HTML富文本)
  value (可选): 初始文本内容，默认为空字符串
  inputType (可选): 输入类型(整数)，使用 InputFieldType 枚举值：
    │ 值 │ 枚举值        │ 说明                  │
    │----│─────────────│─────────────────────│
    │ 0  │ Text         │ 普通文本输入(默认)    │
    │ 1  │ Password     │ 密码输入(显示*替代)   │
    │ 2  │ Multiline    │ 多行文本输入          │
  width (可选): 控件宽度(像素)，0表示自动计算
  height (可选): 控件高度(像素)，仅多行模式有效
返回值:
- >0: 输入框句柄
- 0: 创建失败(使用imgui.getLastError()获取原因)
注意事项:
1. 多行模式需要显式设置高度(height≥60像素)
2. 密码输入框返回值是真实内容(非*号)
3. 输入类型不可运行时修改，需重新创建
4. 使用imgui.inputSetMaskChar自定义密码掩码字符
```

### 创建图片显示控件
```lua
语法: imgui.createImage(parent, path, width, height)
参数说明:
  parent: 父容器句柄(必须)
  path: 图片文件路径(可选)
    - 支持格式: PNG/JPG/BMP等系统支持的格式
    - 传nil或不传创建空图片控件
  width: 图片显示宽度(可选，像素单位)
    - -1: 铺满
返回值:
- 成功返回图片控件句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
注意事项:
1. 路径是绝对路径
```

### 创建颜色选择器控件
```lua
语法: imgui.createColorPicker(parent, title, color, width, height)
参数说明:
  parent: 父容器句柄(必须)
  title: 标题文本(可选，默认"Color")
  color: 初始颜色值(可选，32位ARGB格式，默认0xFF000000黑色不透明)
  width: 控件宽度(可选，像素单位)
  height: 控件高度(可选，像素单位)
颜色格式:
- 32位无符号整数 0xAARRGGBB
  - AA: 透明度(00-FF)
  - RR: 红色分量(00-FF)
  - GG: 绿色分量(00-FF)
  - BB: 蓝色分量(00-FF)
- 常用颜色示例:
  - 红色不透明: 0xFFFF0000
  - 绿色半透明: 0x8000FF00
  - 蓝色透明: 0x000000FF
返回值:
- 成功返回颜色选择器句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
```

### 创建水平进度条控件
```lua
语法: imgui.createProgressBar(parent, progress, width, height)
参数说明:
  parent: 父容器句柄
  progress: 初始进度值(0.0~1.0)
  width (可选): 进度条宽度(像素)，0表示默认宽度
  height (可选): 进度条高度(像素)，0表示使用主题默认高度
返回值:
- >0: 进度条句柄
- 0: 创建失败(使用imgui.getLastError()获取原因)
注意事项:
1. 进度值超过范围会自动钳制(小于0视为0，大于1视为1)
2. 修改进度需使用imgui.setProgress
3. 可通过imgui.setProgressColor自定义颜色
4. 支持平滑动画过渡效果
```

### 创建滑动条控件
```lua
语法: imgui.createSlider(parent, label, min, max, initialPos, width)
参数说明:
  parent: 父容器句柄
  label: 滑动条标签文本
  min: 最小值(整数)
  max: 最大值(整数)
  initialPos: 初始位置(介于min和max之间)
  width: 控件宽度(像素)
返回值:
- 成功返回滑动条句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
注意事项:
1. min必须小于max
2. initialPos必须在[min,max]范围内
3. 宽度和高度为-1时使用默认大小
4. 会触发onControlCreate事件
相关函数:
- imgui.setSlider: 设置滑动条位置
- imgui.getSliderPos: 获取当前滑动条位置
```

### 创建组合框控件
```lua
语法: imgui.createComboBox(parent_handle, items, [width])
参数说明:
  parent_handle: 父控件句柄(窗口或容器控件)
  items: 组合框选项内容，用竖线"|"分隔的字符串(例如"选项1|选项2|选项3")
  width: [可选] 控件宽度，0表示自动宽度(默认0)
返回值:
- 成功返回组合框控件句柄(>0)
- 失败返回0，可通过imgui.getLastError()获取错误信息
注意事项:
1. 需要确保父句柄有效性(使用imgui.isValidHandle验证)
2. 选项字符串最大长度受系统限制(默认2048字符)
3. 选项分隔符"|"会被自动转义，如需包含"|"字符需使用"\|"
4. 创建后可通过setComboBoxSelection设置选中项
5. 控件高度由系统自动决定
6. 选项变更会触发onComboBoxChange事件
7. 线程安全，可在任意线程调用
```

### 创建单选按钮组
```lua
语法: imgui.createRadioGroup(parent, label)
参数说明:
  parent: 父容器句柄(整数)
  label: 单选组的标签文本(字符串)
返回值:
- 成功返回单选组的控件句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
相关函数:
- imgui.addRadioBox: 向单选组添加选项
- imgui.getSelectedRadio: 获取当前选中项
```

### 创建标签栏控件
```lua
语法: local handle = imgui.createTabBar(parent_handle, title)
参数说明:
  parent_handle: 父窗口或父控件句柄(整数)
  title: 标签栏标题(字符串)
返回值:
- 成功返回标签栏句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
注意事项:
1. 需要先创建父窗口/父控件
2. 创建后需要使用imgui.endTabBar()结束标签栏
3. 标签栏内部需要通过imgui.addTabBarItem()添加标签页
```

### 创建表格视图
```lua
语法: imgui.createTableView(parent, title, columns, showheader, width, height)
参数说明:
  parent: 父容器句柄(整数)
  title: 表格标题文本(字符串)
  columns: 列数(整数)
  showheader: 是否显示表头(布尔值)
  width: 表格宽度(浮点数)
  height: 表格高度(浮点数)
返回值:
- 成功返回表格的控件句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
相关函数:
- imgui.setTableHeaderItem: 设置表头文本
- imgui.insertTableRow: 插入表格行
- imgui.setTableItemText: 设置表格单元格文本
```

## IMGUI控件操作

### 添加选项
```lua
语法: imgui.addOptionItem(handle, item_text)
参数说明:
  handle: 控件句柄(由createComboBox创建)
  item_text: 要添加的选项文本(字符串)
返回值:
- 无返回值
- 失败时可通过imgui.getLastError()获取错误信息
注意事项:
1. 需要确保句柄有效性(使用imgui.isValidHandle验证)
2. 选项文本中不应包含分隔符"|"(会被自动转义为"\|")
3. 选项添加后会保持当前选中项不变
4. 会触发onComboBoxItemsChange事件
5. 线程安全，可在任意线程调用
6. 性能提示：批量添加请使用createComboBox或setComboBoxItems
相关函数:
- imgui.createComboBox: 创建带初始选项的组合框
```

### 添加单选选项
```lua
语法: imgui.addRadioBox(handle, text, wrapline)
参数说明:
  handle: 单选组句柄(由createRadioGroup创建)
  text: 选项显示文本(字符串)
  wrapline: 是否自动换行(布尔值)
返回值:
- 成功返回true
- 失败返回false，可通过imgui.getLastError()获取错误信息
相关函数:
- imgui.createRadioGroup: 创建单选组容器
- imgui.getSelectedItemIndex: 获取当前选中项索引
- imgui.getItemText: 获取指定选项文本
```

### 向标签栏添加标签页
```lua
语法: local handle = imgui.addTabBarItem(tabBar_handle, title)
参数说明:
  tabBar_handle: 标签栏句柄(整数)
  title: 标签页标题(字符串)
返回值:
- 成功返回标签页句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
```

### 获取控件当前可见状态
```lua
语法: local visible = imgui.isWidgetVisible(handle)
参数说明:
  handle: 控件句柄(整数)
返回值:
- boolean: true表示控件可见，false表示控件隐藏
```

### 获取复选框/开关控件的当前状态
```lua
语法: imgui.isChecked(handle)
参数说明:
  handle: 有效的控件句柄
返回值:
- boolean: 当前选中状态（true表示选中/开启）
- nil: 获取失败（句柄无效/控件类型不匹配）
注意事项:
1. 返回值反映控件的实时状态（包含动画过渡中的状态）
2. 对已销毁的控件会返回nil
3. 线程安全，可在任何线程调用
```

### 设置复选框/开关控件的选中状态
```lua
语法: imgui.setChecked(handle, state)
参数说明:
  handle: 有效的控件句柄（由createCheckBox/createSwitch创建）
  state: 要设置的选中状态（true/false）
注意事项:
1. 会触发控件的状态变更动画（如开关的滑动效果）
2. 不会触发OnCheck/OnSwitch回调事件
3. 线程安全，可在非UI线程调用
4. 对无效句柄会静默失败
```

### 获取输入框当前文本内容
```lua
语法: imgui.getInputText(handle)
参数说明:
  handle: 有效的输入框句柄（由createInputText创建）
返回值:
- 字符串: 输入框当前文本内容
- nil: 获取失败（句柄无效/控件已销毁）
注意事项:
1. 对于Password类型的输入框，返回的是真实内容而非掩码字符
2. 多行文本会保留换行符(\n)
3. 线程安全，可在任何线程调用
4. 对已销毁的输入框会返回nil
```

### 设置输入框文本内容
```lua
语法: imgui.setInputText(handle, text)
参数说明:
  handle: 输入框控件句柄(由createInputText等函数创建)
  text: 要设置的文本内容(字符串)
返回值:
- 无返回值，失败时可通过imgui.getLastError()获取错误信息
注意事项:
1. 需要确保句柄有效性(使用imgui.isValidHandle验证)
2. 文本内容最大长度受系统限制(默认1024字符)
3. 支持包含转义字符(\n,\t等)，但需符合控件类型：
   - 单行输入框会自动过滤换行符
   - 多行输入框需通过createInputText的inputType参数指定
4. 线程安全，可在任意线程调用
5. 修改后会触发onInputChange事件
6. 密码框类型会显示为掩码字符
```

### 设置输入框类型
```lua
语法: imgui.setInputType(handle, inputType)
参数说明:
  handle: 输入框控件句柄(由createInputField创建)
  inputType: 输入类型，可用值:
    - (0): 普通单行文本
    - (1): 密码输入(显示为***)
    - (2): 多行文本
返回值:
- 无返回值
- 失败时可通过imgui.getLastError()获取错误信息
注意事项:
1. 必须在创建输入框后调用
2. 切换类型会清空现有内容
3. 多行模式下会自动启用垂直滚动条
5. 线程安全，可在任意线程调用
相关函数:
- imgui.getInputText: 获取输入框内容
- imgui.setInputText: 设置输入框内容
```

### 设置或更换显示图片
```lua
语法: imgui.setImage(handle, path)
参数说明:
  handle: 图片控件句柄(必须)
  path: 新图片文件路径
    - 传nil清空当前图片
    - 支持格式同createImage
返回值:
- 无返回值
- 失败时可通过imgui.getLastError()获取错误信息
```

### 设置图像对象
```lua
语法: imgui.setImageFromBitmap(handle, bitmap)
参数说明:
  handle: 图像控件句柄(整数)
  bitmap: 位图对象(Java对象)
返回值: 无
```

### 获取进度条当前进度值
```lua
语法: imgui.getProgressBarPos(handle)
参数说明:
  handle: 有效的进度条句柄
返回值:
- number: 当前进度值（0.0~1.0范围的浮点数）
- nil: 获取失败（句柄无效/控件已销毁）
注意事项:
1. 返回值包含动画过渡中的中间值
2. 对已销毁的进度条会返回nil
3. 线程安全，可在任何线程调用
```

### 设置进度条当前进度值
```lua
语法: imgui.setProgressBarPos(handle, progress)
参数说明:
  handle: 有效的进度条句柄（由createProgressBar创建）
  progress: 进度值（0.0~1.0范围的浮点数）
注意事项:
1. 超出范围的进度值会自动钳制（小于0设为0，大于1设为1）
2. 支持动画过渡效果（需配置动画参数）
3. 线程安全，可在非UI线程调用
4. 修改后会触发进度条重绘
```

### 获取滑动条当前位置
```lua
语法: imgui.getSliderPos(handle)
参数说明:
  handle: 滑动条句柄
返回值:
- 成功返回当前位置值(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
注意事项:
1. 返回值始终在[min,max]范围内
2. 线程安全，可在任意线程调用
3. 性能提示：避免在频繁调用的循环中获取
相关函数:
- imgui.createSlider: 创建滑动条
- imgui.setSlider: 设置滑动条位置
```

## IMGUI表格操作

### 获取表格行数或组合框子项数或单选框控件子项数
```lua
语法: local count = imgui.getItemCount(handle)
参数说明:
  handle: 表格|组合框|单选框 句柄
返回值:
- 成功如果是表返回行数，如果是组合框或者单选框返回子项数(整数，从0开始计数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
```

### 获取下拉项文本
```lua
语法: imgui.getItemText(combo_handle, item_index)
参数说明:
  combo_handle: 组合框控件句柄
  item_index: 要获取的项索引(从0开始)
返回值:
- 成功返回项文本字符串
- 失败返回nil
注意事项:
1. 索引超出范围将返回nil
2. 返回文本已自动处理转义字符
3. 最大返回长度受系统限制(默认1024字符)
```

### 获取组合框|表被选中的项
```lua
语法: imgui.getSelectedItemIndex(handle)
参数说明:
  handle: 组合框|表控件句柄
返回值:
- 成功返回当前选中项的索引(从0开始)
- 无选中项时返回-1
- 失败时返回-2，可通过imgui.getLastError()获取错误信息
注意事项:
1. 需要确保句柄有效性(使用imgui.isValidHandle验证)
2. 返回值说明:
   - ≥0: 有效选中索引
   - -1: 无选中项(组合框为空或未选择)
   - -2: 函数执行出错
3. 与getComboBoxSelection功能相同，两者可互换使用
4. 线程安全，可在任意线程调用
相关函数:
- imgui.setItemSelected: 设置选中项
- imgui.getItemText: 根据索引获取项文本
```

### 设置组合框|表 选中项
```lua
语法: imgui.setItemSelected(handle, index)
参数说明:
  handle: 组合框|表控件句柄
  index: 要选中的项索引(从0开始)
返回值:
- 无返回值
- 失败时可通过imgui.getLastError()获取错误信息
```

### 清空组合框所有项
```lua
语法: imgui.removeAllItems(combo_handle)
参数说明:
  combo_handle: 组合框控件句柄
返回值:
- 无返回值
- 失败时可通过imgui.getLastError()获取错误信息
注意事项:
1. 清空后选中索引将变为-1
3. 性能优于循环调用removeItemAt
```

### 删除组合框指定项
```lua
语法: imgui.removeItemAt(handle, item_index)
参数说明:
  handle: 组合框控件句柄
  item_index: 要删除的项索引(从0开始)
返回值:
- 无返回值
- 失败时可通过imgui.getLastError()获取错误信息
注意事项:
1. 索引超出范围将不执行任何操作
2. 删除后选中项会自动调整
```

### 插入表格行
```lua
语法: imgui.insertTableRow(handle, after)
参数说明:
  handle: 表格句柄(整数)
  after: 插入位置指示(整数)
    - -1: 在开头插入
    - -2: 在末尾插入
    - ≥0: 在指定行索引后插入
返回值:
- 成功返回新插入行的索引(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
注意事项:
1. 行索引从0开始
2. 插入大量数据时建议先禁用表格刷新，完成后再启用
相关函数:
- imgui.setTableItemText: 设置新插入行的内容
- imgui.deleteTableRow: 删除表格行
```

### 删除表格行
```lua
语法: imgui.deleteTableRow(handle, row)
参数说明:
  handle: 表格句柄(由createTableView创建)
  row: 要删除的行索引(从0开始)
返回值:
- 无返回值
- 失败时可通过imgui.getLastError()获取错误信息
相关函数:
- imgui.insertTableRow: 插入新行
- imgui.clearTable: 清空所有行
- imgui.getItemCount: 获取当前行数
```

### 清空表格所有行
```lua
语法: imgui.clearTable(handle)
参数说明:
  handle: 表格句柄(由createTableView创建)
返回值:
- 无返回值
- 失败时可通过imgui.getLastError()获取错误信息
相关函数:
- imgui.deleteTableRow: 删除单行
- imgui.insertTableRow: 插入新行
- imgui.getItemCount: 获取行数
```

### 获取表格单元格文本
```lua
语法: local text = imgui.getTableItemText(handle, row, col)
参数说明:
  handle: 表格句柄(由createTableView创建)
  row: 行索引(从0开始)
  col: 列索引(从0开始)
返回值:
- 成功返回单元格文本内容(字符串)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
注意事项:
1. 行和列索引都不能超过表格当前的范围
2. 如果单元格为空，将返回空字符串("")
3. 返回的文本是原始内容，不包含任何格式化字符
4. 性能提示：批量获取数据时建议先获取行数再循环读取
相关函数:
- imgui.getItemCount: 获取表格总行数
- imgui.setTableItemText: 设置单元格文本
- imgui.createTableView: 创建表格视图
```

## IMGUI事件处理

### 设置按钮点击事件回调
```lua
语法: imgui.setOnClick(buttonHandle, callback)
参数说明:
  buttonHandle: 按钮句柄（由createButton创建）
  callback: 回调函数，格式为 function(handle)
    - handle: 被点击按钮的句柄
返回值: 无
注意事项:
1. 必须在按钮创建成功后调用
2. 回调函数会在UI线程中执行，不宜做耗时操作
3. 每个按钮只能设置一个点击回调，重复设置会覆盖之前的回调
4. 如果需要在回调中修改UI元素，请使用imgui.post延迟处理
5. 回调函数中不要直接销毁按钮本身
```

### 设置复选框状态变更回调
```lua
语法: imgui.setOnCheck(handle, callback)
参数说明:
  handle: 有效的复选框句柄（必须由createCheckBox创建且未被销毁）
  callback: 回调函数，格式为 function(handle, state)
    - handle: 触发事件的复选框句柄
    - state: 新的选中状态（布尔值）
注意事项:
1. 必须先确认imgui.isSupport()返回true
2. 回调函数会在UI线程执行，不宜做耗时操作
3. 重复调用会覆盖之前的回调
4. 要移除回调可设置为nil
5. 回调中不要直接销毁复选框本身
6. 修改UI需使用imgui.post保证线程安全
```

### 设置组合框/单选框选择事件回调
```lua
语法: imgui.setOnSelectEvent(handle, callback)
参数说明:
  handle: 控件句柄(由createComboBox或createRadioGroup创建)
  callback: 回调函数或nil
    - 函数格式: function(handle, index, text)
      - handle: 触发事件的控件句柄
      - index: 选中项的索引(从0开始)
      - text: 选中项的文本内容
返回值:
- 无返回值
- 如果回调执行出错，错误信息会输出到stderr
注意事项:
1. 重复设置会覆盖之前的回调
2. 传入nil可移除现有回调
3. 事件触发时机包括：
   - 用户选择新选项
   - 通过程序调用setSelectedIndex改变选择
4. 线程安全，回调在主UI线程执行
5. 回调中应避免阻塞操作
6. 对于单选框组，会传递当前选中项的索引和文本
相关函数:
- imgui.createComboBox: 创建组合框
- imgui.createRadioGroup: 创建单选框组
- imgui.setSelectedIndex: 设置选中项
- imgui.getSelectedItemText: 获取当前选中文本
```

### 设置表格选择事件回调(扩展版)
```lua
语法: imgui.setOnSelectEventEx(handle, callback)
参数说明:
  handle: 表格句柄(由createTableView创建)
  callback: 回调函数或nil
    - 函数格式: function(handle, row, col, text)
    - 参数说明:
      - handle: 触发事件的表格句柄
      - row: 选中的行索引(从0开始)
      - col: 选中的列索引(从0开始)
      - text: 选中单元格的文本内容
返回值:
- 无返回值
- 如果回调函数执行出错，错误信息会输出到stderr
注意事项:
1. 重复设置会覆盖之前的回调
2. 传入nil可移除现有回调
3. 回调中不要进行耗时操作，以免阻塞UI线程
4. 事件触发时机：当用户点击表格单元格时
5. 线程安全，但回调会在主UI线程执行
6. 回调函数内部错误不会影响程序继续运行
相关函数:
- imgui.createTableView: 创建表格控件
- imgui.setTableItemText: 设置单元格内容
- imgui.getTableItemText: 获取单元格内容
```

### 设置滑动条值变化事件回调
```lua
语法: imgui.setOnSliderEvent(handle, callback)
参数说明:
  handle: 滑动条控件句柄(由createSlider创建)
  callback: 回调函数或nil
    - 函数格式: function(handle, value)
    - 参数说明:
      * handle: 触发事件的滑动条句柄
      * value: 当前滑块位置值(整数)
返回值:
- 无返回值
- 如果回调函数执行出错，错误信息会输出到stderr
注意事项:
1. 事件触发时机包括：
   - 用户拖动滑块时实时触发
   - 用户点击滑动条轨道跳转位置时
   - 通过程序调用setSlider改变值时
2. 重复设置会覆盖之前的回调
3. 传入nil可移除现有回调
4. 回调中不要进行耗时操作，以免阻塞UI线程
5. 线程安全，但回调会在主UI线程执行
6. 值变化频率可能很高，建议做适当节流处理
相关函数:
- imgui.createSlider: 创建滑动条控件
- imgui.setSlider: 设置滑动条位置
- imgui.getSliderPos: 获取滑动条当前位置
```

## IMGUI图形绘制

### 创建圆形
```lua
语法: imgui.createCircle(x, y, radius, color, filled, segments)
参数说明:
  x: 圆心X坐标(数字)
  y: 圆心Y坐标(数字)
  radius: 半径(数字)
  color: 颜色值(十六进制整数，格式0xAARRGGBB)
  filled: 是否填充(布尔值)
  segments: 分段数(整数，值越大圆越平滑)
返回值:
- 成功返回圆形句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
注意事项:
- 推荐分段数在12-64之间，平衡性能和质量
- 要绘制椭圆可以使用imgui.createEllipse()
```

### 创建矩形
```lua
语法: imgui.createRectangle(x, y, x1, y1, color, filled, rounding)
参数说明:
  x: 矩形左上角X坐标(数字)
  y: 矩形左上角Y坐标(数字)
  x1: 矩形右下角X坐标(数字)
  y1: 矩形右下角Y坐标(数字)
  color: 颜色值(十六进制整数，格式0xAARRGGBB)
  filled: 是否填充(布尔值，true为填充，false为边框)
  rounding: 圆角半径(数字，0为直角)
返回值:
- 成功返回矩形句柄(整数)
- 失败返回nil，可通过imgui.getLastError()获取错误信息
```

### 创建线段
```lua
语法: handle = imgui.createLine(x1,y1,x2,y2,color,thickness)
参数说明:
  x1,y1: 起点坐标
  x2,y2: 终点坐标
  color: 颜色值(0xAARRGGBB)
  thickness: 线宽(像素)
返回值: 线段句柄(整数)
```

### 创建多边形
```lua
语法: handle = imgui.createPolygon(points, color, closed, filled, thickness)
参数说明:
  points: 顶点数组(table of {x,y} tables)
  color: 颜色(0xAARRGGBB格式)
  closed: 是否闭合(布尔值)
  filled: 是否填充(布尔值)
  thickness: 线宽(数字)
返回值:
- 成功返回图形句柄(整数)
- 失败返回nil
```

### 创建文本图形
```lua
语法: handle = imgui.createShapeText(x,y,w,h,text,text_color,bg_color,has_background,font_scale)
参数说明:
  x,y: 位置坐标
  w,h: 文本框尺寸
  text: 文本内容(字符串)
  text_color: 文字颜色(0xAARRGGBB)
  bg_color: 背景颜色(0xAARRGGBB)
  has_background: 是否显示背景(布尔值)
  font_scale: 字体缩放系数(建议0.5~3.0)
返回值: 文本图形句柄(整数)
```

### 创建位图形状
```lua
语法: handle = imgui.createBitmapShape(x,y,width,height,bitmap)
参数说明:
  x,y: 形状位置坐标
  width: 形状宽度
  height: 形状高度
  bitmap: 位图对象(Java对象)
返回值: 位图形状句柄(整数)
```

## IMGUI图形操作

### 判断图形是否可见
```lua
语法: result = imgui.isShapeVisibility(handle)
参数说明:
  handle: 图形句柄
返回值: 布尔类型
```

### 删除图形对象
```lua
语法: result = imgui.removeShape(handle)
参数说明:
  handle: 要删除的图形句柄(整数)
返回值:
- 0: 删除成功
- -1: 删除失败(无效句柄或已删除)
注意事项:
1. 删除后句柄立即失效
2. 建议在窗口关闭时批量删除所有图形
3. 重复删除不会报错但返回-1
```

### 设置图形位置
```lua
语法: imgui.setShapePosition(handle,x,y)
参数说明:
  handle: 图形句柄
  x,y: 新坐标(对于多边形是整体偏移)
返回值: 0成功, -1失败
```

### 设置形状边框厚度
```lua
语法: success = imgui.setShapeThickness(handle, thickness)
参数说明:
  handle: 形状句柄(整数)
  thickness: 边框厚度值(浮点数，必须大于0)
返回值: 布尔值(true表示设置成功，false表示失败)
```

### 设置位图形状
```lua
语法: imgui.setBitmapShape(handle,bitmap)
参数说明:
  handle: 之前创建的位图形状句柄
  bitmap: 新的位图对象(Java对象)
返回值: 无
```

### 修改文本内容
```lua
语法: imgui.setShapeTextString(handle, new_text)
参数说明:
  handle: 文本图形句柄
  new_text: 新文本内容
返回值: 是否修改成功(布尔值)
```

### 设置文本颜色
```lua
语法: success = imgui.setShapeTextColor(handle, new_color)
参数说明:
  handle: 文本图形句柄
  new_color: 新颜色(0xAARRGGBB)
返回值: 是否修改成功(布尔值)
```

### 设置文本背景
```lua
语法: imgui.setShapeTextBackground(handle, bg_color, has_bg)
参数说明:
  handle: 文本图形句柄
  bg_color: 背景颜色(0xAARRGGBB)
  has_bg: 是否显示背景
返回值: 是否修改成功(布尔值)
```

### 设置文本图形字体缩放比例
```lua
语法: success = imgui.setShapeTextFontScale(handle, scale)
参数说明:
  handle: 文本图形句柄(整数)
  scale: 缩放比例(浮点数，建议范围0.5~3.0)
返回值:
- true: 设置成功
- false: 设置失败(可通过imgui.getLastError()获取错误信息)
注意事项:
1. 缩放后可能需要手动调整文本框尺寸
2. 极端值可能导致文字渲染异常
3. 缩放是相对于创建时的初始大小
```

# UI交互方法

## 窗口管理

### closeWindow

**关闭窗口**

```lua
closeWindow(handle,isaveconfig)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `isaveconfig` - 是否保存当前界面的配置

**功能说明**

- 关闭指定句柄的窗口，可以选择是否保存当前配置

------

### showUI

**显示一个自定义的界面**

```lua
showUI(uifile,[w],[h],[onUIEvent])
```

**参数说明**

- `uifile` - 可以是一个ui文件名称
- `w,h` - 整数型，可选项,设置此值来改变窗口的大小，-1表示填满
- `onUIEvent` - 函数类型,可选项,懒人高级版本和之前版本有区别的地方，这个回调函数会处理所有ui的事件

**回调函数参数说明**

```lua
function onUIEvent(handle,event,arg1,arg2,arg3)
```

- `handle` - 窗口的句柄
- `event` - 字符串类型,具体事件:
  - "onload": 窗口正常加载完成时会触发此消息
  - "onclick": 当按钮,多选框，单选框被点击的时候会触发此消息
  - "onchecked": 多选框被选中或者反选的时候会触发这个消息
  - "onselected": 单选框或者下拉框被选中的时候会触发此消息
  - "onwebviewjsevent": 网页控件当js调用window.bridge.sendToLua时会触发此事件
  - "onclose": 窗口关闭事件

**返回值**

- 返回界面配置的json字符串

**注意事项**

- 当onUIEvent参数不填写的时候，继续和退出按钮将接管用户点击事件，点击退出的时候会直接结束脚本，继续将保存用户配置并关闭窗口

------

### showUIEx

**显示一个自定义的界面(扩展版本)**

```lua
showUIEx(uifile,[x],[y],[w],[h],[onUIEvent])
```

**参数说明**

- `uifile` - 可以是一个ui文件名称或路径，也可以是ui文件的字符串形式
- `x,y` - 整数型, 可选项,设置悬浮窗的位置
- `w,h` - 整数型，可选项,设置此值来改变窗口的大小，-1表示填满
- `onUIEvent` - 函数类型,可选项,回调函数处理所有ui的事件

**功能说明**

- 这个是showUI的扩展版本，该方法可以设置窗口的坐标位置

**返回值**

- 返回界面配置的json字符串

------

## 控件操作

### getUICheck

**获取单选框状态**

```lua
getUICheck(handle,pageidx,idname)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id

------

### setUICheck

**设置多选框被选中或者反选**

```lua
setUICheck(handle,pageidx,idname,selected)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id
- `selected` - 是否被选中

------

### getUIEnable

**获取控件是否可用**

```lua
getUIEnable(handle,pageidx,idname)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id

------

### setUIEnable

**设置控件是否可用**

```lua
setUIEnable(handle,pageidx,idname,enable)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id
- `enable` - 布尔类型，true表示可用，false表示不可用

------

### getUISelectText

**获取单选框或者下拉框当前选中项文本**

```lua
getUISelectText(handle,pageidx,idname)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id

**返回值**

- 选中控件的文本内容

------

### getUISelected

**获取单选框或者下拉框当前选中项索引**

```lua
getUISelected(handle,pageidx,idname)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id

**返回值**

- 选中控件的序号

------

### setUISelect

**设置单选框或者下拉框选项被选中**

```lua
setUISelect(handle,pageidx,idname,index)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id
- `index` - 要选中项的索引(从0开始)

------

### getUIText

**获取控件显示的文本内容**

```lua
getUIText(handle,pageidx,idname)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id

**功能说明**

- 获取文本框，输入框，按钮,多选框控件显示的文本内容

------

### setUIText

**设置控件文本**

```lua
setUIText(handle,pageidx,idname,text)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id
- `text` - 要设置的文本字符串

**功能说明**

- 设置文本框，输入框，按钮,多选框等显示的文本或名称

------

### getUIVisible

**获取当前控件可见的值**

```lua
getUIVisible(handle,pageidx,idname)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id

------

### setUIVisible

**设置控件隐藏或可见**

```lua
setUIVisible(handle,pageidx,idname,visible)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id
- `visible` - 整数型，为0表示可见，为8表示隐藏不占用空间大小，为4表示隐藏但是还是占据空间大小

------

### setUIBackground

**设置窗口或者控件的背景**

```lua
setUIBackground(handle,pageidx,idname,bg)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id
- `bg` - 要设置的背景，可以时颜色也可以时图片，图片放到资源里面(直接填写名称即可)或者界面文件夹里面，如果时界面文件夹里面请填写相对路径

------

### setUITextColor

**修改控件字体颜色**

```lua
setUITextColor(handle,pageidx,idname,textcolor)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id
- `textcolor` - 16进制字符串例如:"0xff00ff00" 或者 "#ff00ff00"

**功能说明**

- 修改控件字体颜色,包括文本框，输入框

------

### getUIWebViewUrl

**获取当前浏览器的地址**

```lua
getUIWebViewUrl(handle,pageidx,idname)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id

------

### setUIWebViewUrl

**设置浏览器地址**

```lua
setUIWebViewUrl(handle,pageidx,idname,url)
```

**参数说明**

- `handle` - 当前窗口的句柄
- `pageidx` - 要设置的控件所在page页的索引(从0开始)
- `idname` - 控件的id
- `url` - 浏览器的地址

------

## 配置管理

### setUIConfig

**把指定的配置json字符串写入到configname中**

```lua
setUIConfig(configname,configstr)
```

**参数说明**

- `configname` - ui的配置名称
- `configstr` - 配置的具体json字符串

------

## 可拖动视图

### createDragView

**创建可拖动视图**

```lua
createDragView(x, y, width, height, imagePath)
```

**参数说明**

- `x` - 视图初始X坐标(必须，屏幕坐标系的像素值)
- `y` - 视图初始Y坐标(必须，屏幕坐标系的像素值)
- `width` - 视图宽度(必须，像素单位)
- `height` - 视图高度(必须，像素单位)
- `imagePath` - 文本型,表示提示信息的背景颜色，可以是ARGB，也可以是图片文件名称

**返回值**

- 成功返回可拖动视图的句柄(整数)
- 失败返回nil

------

### getDragViewPos

**获取可拖动视图当前位置**

```lua
getDragViewPos(handle)
```

**参数说明**

- `handle` - 可拖动视图句柄(必须，由createDragView创建)

**返回值**

- 成功返回三个值:
  - 第一个值为true表示成功
  - 第二个值为当前视图中心点X坐标
  - 第三个值为当前视图中心点Y坐标
- 失败返回false

------

### setDragViewOnClick

**设置可拖动视图点击回调**

```lua
setDragViewOnClick(handle, callback)
```

**参数说明**

- `handle` - 可拖动视图的句柄(由createDragView创建)
- `callback` - 点击回调函数，格式为 function(handle,x,y)
  - `handle` - 被点击的视图句柄
  - `x,y` - 点击位置的屏幕坐标

------

### removeDragView

**移除可拖动视图**

```lua
removeDragView(handle)
```

**参数说明**

- `handle` - 可拖动视图句柄(必须，由createDragView创建)

**注意事项**

- 移除后句柄将失效，不可再次使用

------

## HUD显示

### createHUD

**创建一个HUD用于显示**

```lua
createHUD()
```

**功能说明**

- HUD是一个悬浮在屏幕上的画面可以实时更新

**返回值**

- 整数类型,是一个HUD实例句柄

------

### showHUD

**显示HUD或者刷新**

```lua
showHUD(id,text,size,color,bg,pos,x,y,width,height,[leftpadding,toppadding,rightpadding,bottompadding],[align_text])
```

**参数说明**

- `id` - 整数型,用于标示HUD
- `text` - 文本型,提示信息,将在屏幕上以HUD形式显示
- `size` - 整数型,表示提示信息的字体大小
- `color` - 文本型,表示提示信息的字体颜色，格式为ARGB
- `bg` - 文本型,表示提示信息的背景颜色，可以是ARGB，也可以是图片文件名称
- `pos` - 整数型,表示提示信息的原点位置，0 - 左上角，1 - 居中，2 - 水平居中， 3 - 垂直居中
- `x,y` - 整数型,表示提示信息相对原点的坐标偏移值
- `width,height` - 整数型,表示提示信息显示的宽高
- `leftpadding` - 左边内边距默认不填写是3
- `toppadding` - 上边内边距默认不填写是3
- `rightpadding` - 右边内边距默认不填写是3
- `bottompadding` - 下边内边距默认不填写是3
- `align_text` - 文本对齐方式默认不写为1， 0左上 1居中 2上水平居中 3左垂直居中 4左下 5右上 6右边垂直居中 7右下 8下水平居中

**功能说明**

- 显示更新HUD

------

### hideHUD

**隐藏并销毁HUD**

```lua
hideHUD(id)
```

**参数说明**

- `id` - 整数型,用于标示HUD

**注意事项**

- 调用此方法后不能再用当前id句柄继续showHUD，如果需要调用showHUD需要重新createHUD

------

## 消息提示

### toast

**屏幕上显示信息**

```lua
toast(text,[x,y,textsize])
```

**参数说明**

- `text` - 要显示的字符串
- `x` - 整数型,屏幕横向坐标，可以不填写默认为0
- `y` - 整数型,屏幕纵向坐标，可以不填写默认为0
- `textsize` - 整数型,字体大小，可以不填写默认为12

**功能说明**

- 在屏幕指定位置弹出信息框，当坐标x,y同时为0的时候，默认向下横向居中显示，5秒后自动消失

------

### hideToast

**关闭消息显示**

```lua
hideToast()
```

**功能说明**

- 关闭当前显示的toast消息

------

# 扩展方法

## Android系统扩展

### LuaEngine.getContext

**获取android上下文对象**

```lua
LuaEngine.getContext()
```

**返回值**

- 获取android上下文对象

------

### LuaEngine.loadApk

**加载一个apk插件**

```lua
LuaEngine.loadApk(name)
```

**参数说明**

- `name` - 字符串类型，如果apk插件放到资源中则只需要写名字，如果需要从sd卡加载则这里写绝对路径

**功能说明**

- 此函数还可以加载autojs的插件

**返回值**

- 返回一个插件对象

------

### import

**加载java类**

```lua
import(className)
```

**参数说明**

- `className` - 字符串类型,表示一个java类名

**功能说明**

- 此方法是为了能更加方便扩展安卓原生调用，具体详细的使用例子请进群下载

**注意事项**

- 此方法只支持android端，后续ios端不支持此方法

------

## 线程管理

### newThread

**启动一个新的线程**

```lua
newThread(callback,...)
```

**参数说明**

- `callback` - 线程回调方法
- `...` - 可变参数

**返回值**

- 线程标识符，为空表示失败

------

### beginThread

**启动一个新的线程(增强版)**

```lua
beginThread(callback,...)
```

**参数说明**

- `callback` - 线程回调方法
- `...` - 可变参数

**功能说明**

- 启动一个新的线程，注意新线程无法使用exitScript 和 restartScript，但是可以通过setTimer向主线程发送一个回调方法中去退出或者重启脚本，为了稳定性同时创建的线程最大并发数不能超过10个

**返回值**

- 线程对象

------

### th:stopThread

**停止线程**

```lua
th:stopThread()
```

**功能说明**

- 停止指定的线程

------

## 加密和编码

### MD5

**获取字符串的MD5码**

```lua
MD5(string)
```

**参数说明**

- `string` - 要MD5编码的字符串

------

### fileMD5

**获取文件的MD5码**

```lua
fileMD5(string)
```

**参数说明**

- `string` - 要MD5编码的文件绝对路径

------

### encodeBase64

**字符串base64编码**

```lua
encodeBase64(str)
```

**参数说明**

- `str` - 待编码的字符串

**返回值**

- base64编码结果

------

### decodeBase64

**base64字符串解码**

```lua
decodeBase64(str)
```

**参数说明**

- `str` - 待解码的base64字符串

**返回值**

- base64解码结果

------

### encodeUrl

**格式编码为url格式**

```lua
encodeUrl(str)
```

**参数说明**

- `str` - 待编码为url的字符串

**返回值**

- 编码后的url字符串

------

### decodeUrl

**url格式解码**

```lua
decodeUrl(str)
```

**参数说明**

- `str` - 待解码的url字符串

**返回值**

- url解码结果

------

### getFileBase64

**获取文件内容的base64编码**

```lua
getFileBase64(path)
```

**参数说明**

- `path` - 文件的绝对路径

**返回值**

- base64编码结果

------

## 文件操作扩展

### readFile

**读取文件所有内容**

```lua
readFile(path)
```

**参数说明**

- `path` - 文件绝对路径或者主资源文件中的文件名

**返回值**

- 读取的文件数据

------

### writeFile

**写字符串到文件**

```lua
writeFile(path,str,[append])
```

**参数说明**

- `path` - 文件绝对路径
- `str` - 要输入的字符串内容
- `append` - 是否追加

**返回值**

- 布尔类型

------

### fileExist

**文件是否存在**

```lua
fileExist(file)
```

**参数说明**

- `file` - 文件绝对路径

**返回值**

- true表示存在false不存在

------

### fileSize

**获取文件大小**

```lua
fileSize(path)
```

**参数说明**

- `path` - 文件绝对路径

**返回值**

- 文件长度单位是字节

------

### mkdir

**创建文件夹**

```lua
mkdir(dir)
```

**参数说明**

- `dir` - 文件夹绝对路径

**返回值**

- true表示成功false失败

------

### delfile

**删除文件或文件夹**

```lua
delfile(dir)
```

**参数说明**

- `dir` - 文件夹绝对路径

**返回值**

- true表示成功false失败

------

## 图像处理扩展

### getImage

**读取本地图片数据**

```lua
getImage(path)
```

**参数说明**

- `path` - 文件的路径

**功能说明**

- 读取本地图片数据图片类型可以是png bmp jpg

**返回值**

- 宽度，高度，像素数组

------

### binaryImage

**二值化本地图片**

```lua
binaryImage(srcimage,dstimage,[ThresholdTypes],[thresh],[maxthresh])
```

**参数说明**

- `srcimage` - 源图片
- `dstimage` - 生成后的二值化图片
- `ThresholdTypes` - 整数型,(THRESH_BINARY = 0,THRESH_BINARY_INV = 1,THRESH_TRUNC = 2,THRESH_TOZERO = 3,THRESH_TOZERO_INV = 4,THRESH_MASK = 7,THRESH_OTSU = 8,THRESH_TRIANGLE = 16)具体含义请查看opencv文档，默认不写是8(THRESH_OTSU)
- `thresh` - 整数型，阈值默认不写是150
- `maxthresh` - 整数型，最大阈值，默认不写255

**功能说明**

- 二值化本地图片,图片格式可以是png bmp jpg三种类型

**返回值**

- 1表示成功0表示失败

------

### binaryRect

**区域截图二值化**

```lua
binaryRect(save,l,t,r,b,[ThresholdTypes],[thresh],[maxthresh])
```

**参数说明**

- `save` - 最终生成的图片
- `l,t,r,b` - 区域范围
- `ThresholdTypes` - 整数型,(THRESH_BINARY = 0,THRESH_BINARY_INV = 1,THRESH_TRUNC = 2,THRESH_TOZERO = 3,THRESH_TOZERO_INV = 4,THRESH_MASK = 7,THRESH_OTSU = 8,THRESH_TRIANGLE = 16)具体含义请查看opencv文档，默认不写是8(THRESH_OTSU)
- `thresh` - 整数型，阈值默认不写是150
- `maxthresh` - 整数型，最大阈值，默认不写255

**功能说明**

- 区域截图二值化,保存的图片格式可以是png bmp jpg三种类型

**返回值**

- 1表示成功0表示失败

------

### rotateImage

**旋转本地图片**

```lua
rotateImage(path,rotate)
```

**参数说明**

- `path` - 文件的路径
- `rotate` - 可以是90 180 270三种值，此处为顺时针旋转

**功能说明**

- 旋转本地图片,图片格式可以是png bmp jpg三种类型

**返回值**

- 1表示成功0表示失败

------

### scaleImage

**缩放图片**

```lua
scaleImage(src,dst,w,h)
```

**参数说明**

- `src` - 原始路径
- `dst` - 目标路径
- `w,h` - 生成图片长宽

**功能说明**

- 缩放图片,图片格式可以是png bmp jpg三种类型

**返回值**

- true表示成功false表示失败

------

## OCR扩展

### bdOcr

**百度ocr**

```lua
bdOcr(url,apikey,secretkey,[type])
```

**参数说明**

- `url` - 可以是网络图片也可以是本地图片
- `apikey` - 需要自己在百度进行申请
- `secretkey` - 需要自己在百度进行申请
- `type` - 可以取值0,1,2,3 ,0:通用文字识别 1:通用文字识别（含位置信息版）,2:通用文字识别（高精度版）,3:通用文字识别（高精度含位置版）

**返回值**

- 返回一个json 例子如:{"words_result":[{"words":"名称:ur.r"},{"words":"大小:1KB"}],"log_id":1302556202114220032,"words_result_num":2}

------

### qrDecode

**二维码解析**

```lua
qrDecode(pngpath)
```

**参数说明**

- `pngpath` - 需要解析的二维码路径

**返回值**

- ret,text ret为1表示成功0表示失败 text是返回解析的结果

------

## 数据库操作

### mysql.connectSQL

**连接mysql数据库**

```lua
mysql.connectSQL(host,port,database,user,password,timeout)
```

**参数说明**

- `host` - 字符串类型,MYSQL服务器地址
- `port` - 数字类型,MYSQL服务端口号
- `database` - 字符串类型,要访问的数据库名称
- `user` - 字符串类型,访问数据库的账号名称（如果没有不用填写）
- `password` - 字符串类型,访问数据库密码（如果没有不用填写）
- `timeout` - 超时时长，单位是毫秒

**返回值**

- 返回两个值，第一个表示该连接的唯一句柄如果为空表示失败，第二个是如果失败则返回失败原因，否则返回空

------

### mysql.executeSQL

**执行sql语句**

```lua
mysql.executeSQL(handle,sql)
```

**参数说明**

- `handle` - 整数类型，由mysql_connect返回
- `sql` - 字符串类型，sql语句，请参考sql语法

**返回值**

- 返回值有两个个第一个布尔类型表示是否成功，第二个是如果失败则返回失败原因，否则返回空

------

### mysql.executeQuerySQL

**执行sql语句并返回结果**

```lua
mysql.executeQuerySQL(handle,sql)
```

**参数说明**

- `handle` - 整数类型，由mysql_connect返回
- `sql` - 字符串类型，sql语句，请参考sql语法

**返回值**

- 返回值有三个第一个返回的结果，第二个是如果失败则返回失败原因，否则返回空

------

### mysql.closeSQL

**关闭数据库连接**

```lua
mysql.closeSQL(handle)
```

**参数说明**

- `handle` - java类型，由mysql.connectSQL返回

**返回值**

- 布尔值

------

## HUD显示

### createHUD

**创建一个HUD用于显示**

```lua
createHUD()
```

**功能说明**

- HUD是一个悬浮在屏幕上的画面可以实时更新

**返回值**

- 整数类型,是一个HUD实例句柄

------

### showHUD

**显示HUD或者刷新**

```lua
showHUD(id,text,size,color,bg,pos,x,y,width,height,[leftpadding,toppadding,rightpadding,bottompadding],[align_text])
```

**参数说明**

- `id` - 整数型,用于标示HUD
- `text` - 文本型,提示信息,将在屏幕上以HUD形式显示
- `size` - 整数型,表示提示信息的字体大小
- `color` - 文本型,表示提示信息的字体颜色，格式为ARGB
- `bg` - 文本型,表示提示信息的背景颜色，可以是ARGB，也可以是图片文件名称
- `pos` - 整数型,表示提示信息的原点位置，0 - 左上角，1 - 居中，2 - 水平居中， 3 - 垂直居中
- `x,y` - 整数型,表示提示信息相对原点的坐标偏移值
- `width,height` - 整数型,表示提示信息显示的宽高
- `leftpadding` - 左边内边距默认不填写是3
- `toppadding` - 上边内边距默认不填写是3
- `rightpadding` - 右边内边距默认不填写是3
- `bottompadding` - 下边内边距默认不填写是3
- `align_text` - 文本对齐方式默认不写为1， 0左上 1居中 2上水平居中 3左垂直居中 4左下 5右上 6右边垂直居中 7右下 8下水平居中

**功能说明**

- 显示更新HUD

------

### hideHUD

**隐藏并销毁HUD**

```lua
hideHUD(id)
```

**参数说明**

- `id` - 整数型,用于标示HUD

**注意事项**

- 调用此方法后不能再用当前id句柄继续showHUD，如果需要调用showHUD需要重新createHUD

------

## 消息提示

### toast

**屏幕上显示信息**

```lua
toast(text,[x,y,textsize])
```

**参数说明**

- `text` - 要显示的字符串
- `x` - 整数型,屏幕横向坐标，可以不填写默认为0
- `y` - 整数型,屏幕纵向坐标，可以不填写默认为0
- `textsize` - 整数型,字体大小，可以不填写默认为12

**功能说明**

- 在屏幕指定位置弹出信息框，当坐标x,y同时为0的时候，默认向下横向居中显示，5秒后自动消失

------

### hideToast

**关闭消息显示**

```lua
hideToast()
```

**功能说明**

- 关闭当前显示的toast消息

------